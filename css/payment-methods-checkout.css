/**
 * Payment Methods Checkout Styles
 */
.wc_payment_method[class*="payment_method_payop_"] {
  position: relative;
  border-radius: 5px;
  transition: background-color 0.2s ease;
}

.wc_payment_method[class*="payment_method_payop_"]:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

.wc_payment_method[class*="payment_method_payop_"].payop-selected {
  background-color: rgba(0, 124, 186, 0.1);
  border-left: 3px solid #007cba;
  padding-left: 12px;
}

.wc_payment_method[class*="payment_method_payop_"] label {
  display: flex !important;
  align-items: center;
}

.wc_payment_method[class*="payment_method_payop_"] label img {
  max-height: 30px;
  margin-right: 12px;
  padding: 4px;
  background: #fff;
  border-radius: 3px;
  border: 1px solid #f0f0f0;
}

.payop-payment-method-logo {
  max-height: 24px;
  margin-right: 8px;
}

/* Payment method fields container */
.payop-payment-fields {
    margin-top: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.payop-payment-fields .form-row {
    margin-bottom: 15px;
}

.payop-payment-fields .form-row:last-child {
    margin-bottom: 0;
}

/* Field labels */
.payop-payment-fields label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

.payop-payment-fields .required {
    color: #dc3232;
}

/* Input fields */
.payop-payment-fields input[type="text"],
.payop-payment-fields input[type="email"],
.payop-payment-fields input[type="tel"],
.payop-payment-fields input[type="number"],
.payop-payment-fields select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.5;
    transition: border-color 0.3s ease;
}

.payop-payment-fields input:focus,
.payop-payment-fields select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
}

/* Error states */
.payop-payment-fields .field-error {
    border-color: #dc3232 !important;
    box-shadow: 0 0 0 1px #dc3232 !important;
}

.payop-field-error-message {
    color: #dc3232;
    font-size: 0.85em;
    margin-top: 5px;
    display: block;
    font-style: italic;
}

/* Select field styling */
.payop-payment-fields select {
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'%3E%3Cpath fill='%23666' d='m2 0-2 2h4zm0 5 2-2h-4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 12px;
    padding-right: 40px;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

/* Block Checkout Styles */
.wc-block-components-radio-control-accordion-content
  .payop-payment-method-logo {
  height: 24px;
  margin-right: 8px;
  vertical-align: middle;
}

.wc-block-components-payment-method-icons {
  justify-content: flex-start !important;
}

.wc-block-components-payment-method-label[for^="radio-control-wc-payment-method-options-payop_"] {
  display: flex;
  align-items: center;
}

.wc-block-components-payment-method-label[for^="radio-control-wc-payment-method-options-payop_"] img {
  max-height: 24px;
  margin-right: 8px;
}

/* WooCommerce Blocks payment fields */
.wp-block-woocommerce-checkout .payop-payment-fields {
    margin-top: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .payop-payment-fields {
        padding: 10px;
        margin-top: 10px;
    }
    
    .payop-payment-fields .form-row {
        margin-bottom: 12px;
    }
    
    .payop-payment-fields input,
    .payop-payment-fields select {
        padding: 10px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
}

/* Accessibility improvements */
.payop-payment-fields input:focus,
.payop-payment-fields select:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .payop-payment-fields {
        border-color: #000;
        background-color: #fff;
    }
    
    .payop-payment-fields input,
    .payop-payment-fields select {
        border-color: #000;
    }
    
    .payop-payment-fields .field-error {
        border-color: #ff0000 !important;
    }
}

/* Mobile Styles */
@media screen and (max-width: 768px) {
  .wc_payment_method[class*="payment_method_payop_"] label {
    flex-wrap: wrap;
  }

  .wc_payment_method[class*="payment_method_payop_"] label img {
    margin-bottom: 8px;
  }
}
