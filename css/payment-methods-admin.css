/**
 * Payment Methods Admin Styles
 */
.payop-payment-methods-wrapper {
  margin: 20px 0;
  max-width: 800px;
  background: #fff;
  padding: 15px;
  border: 1px solid #ddd;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.payop-payment-methods-section h3 {
  margin-bottom: 10px;
}

.payop-payment-methods-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
}

.payop-action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.payop-spinner {
  visibility: hidden;
  display: inline-block;
  vertical-align: middle;
  float: none;
  margin-left: 5px;
  width: 20px;
  height: 20px;
}

.payop-selected-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.payop-selected-count {
  font-weight: 500;
  font-size: 13px;
  color: #555;
  background: #f7f7f7;
  padding: 5px 10px;
  border-radius: 3px;
}

.payop-admin-notice {
  display: none;
  padding: 10px 15px;
  margin: 15px 0;
  border-left: 4px solid #00a0d2;
  background: #f8f8f8;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.payop-admin-notice.notice-success {
  border-left-color: #46b450;
}

.payop-admin-notice.notice-error {
  border-left-color: #dc3232;
}

#payop-payment-methods-list {
  margin: 0;
  padding: 0;
  border: 1px solid #ddd;
  background: #fff;
  max-height: 500px;
  overflow-y: auto;
}

.payop-payment-methods-empty {
  padding: 20px;
  text-align: center;
  margin: 0;
  color: #777;
  background: #f9f9f9;
}

.payop-method-item {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-bottom: 1px solid #f0f0f0;
  margin: 0;
  background: #fff;
  transition: background-color 0.2s ease;
}

.payop-method-item:hover {
  background-color: #f9f9f9;
}

.payop-method-item:last-child {
  border-bottom: none;
}

.payop-method-handle {
  cursor: move;
  color: #aaa;
  margin-right: 8px;
}

.payop-method-checkbox {
  margin-right: 15px;
}

.payop-method-logo {
  margin-right: 15px;
  width: 60px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.payop-method-logo img {
  max-width: 60px;
  max-height: 30px;
  display: inline-block;
  vertical-align: middle;
  object-fit: contain;
}

.payop-method-details {
  flex: 1;
}

.payop-method-title {
  display: block;
  font-weight: 600;
  margin-bottom: 5px;
  cursor: pointer;
}

.payop-method-meta {
  color: #777;
  font-size: 12px;
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.payop-method-meta span {
  margin-right: 12px;
}

/* UI Sortable Styles */
.payop-method-item.ui-sortable-helper {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  border: 1px solid #ddd;
}

.payop-method-item.ui-sortable-placeholder {
  visibility: visible !important;
  background-color: #f3f3f3;
  border: 1px dashed #ddd;
  height: 54px;
}

/* Credentials alert for payment methods */
.payop-credentials-notice {
  margin: 10px 0 20px;
  padding: 10px 15px;
  background: #fcf9e8;
  border-left: 4px solid #dba617;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.payop-jwt-required {
  color: #d63638;
  font-weight: 500;
  font-style: italic;
}

@media screen and (max-width: 782px) {
  .payop-payment-methods-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .payop-method-item {
    flex-wrap: wrap;
  }

  .payop-method-logo {
    order: -1;
    margin-left: 35px;
  }

  .payop-method-details {
    width: 100%;
    margin-top: 10px;
    margin-left: 35px;
  }
}
