<?php
/*
Plugin Name: Payop WooCommerce Payment Gateway
Plugin URI: https://wordpress.org/plugins/payop-woocommerce/
Description: Payop: Online payment processing service ➦ Accept payments online by 150+ methods from 170+ countries. Payments gateway for Growing Your Business in New Locations and fast online payments
Author URI: https://payop.com/
Version: 3.0.10
Requires at least: 6.3
Tested up to: 6.7.2
Requires PHP: 7.4
WC requires at least: 8.3
WC tested up to: 9.7.1
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Domain Path: /languages
*/

if (!defined('ABSPATH')) {
	exit;
}

define('PAYOP_PLUGIN_FILE', __FILE__);
define('PAYOP_PLUGIN_PATH', plugin_dir_path(PAYOP_PLUGIN_FILE));
define('PAYOP_PLUGIN_URL', plugin_dir_url(PAYOP_PLUGIN_FILE));
define('PAYOP_PLUGIN_BASENAME', plugin_basename(PAYOP_PLUGIN_FILE));
define('PAYOP_LANGUAGES_PATH', plugin_basename(dirname(__FILE__)) . '/languages/');
define('PAYOP_PAYMENT_GATEWAY_NAME', 'payop');
define('PAYOP_INVITATE_RESPONSE', 'payop_invitate_response');
define('PAYOP_PLUGIN_NAME', 'Payop WooCommerce Payment Gateway');
define('PAYOP_MIN_PHP_VERSION', '7.4');
define('PAYOP_MIN_WP_VERSION', '6.3');
define('PAYOP_MIN_WC_VERSION', '8.3');
define('PAYOP_IPN_VERSION_V1', 'V1');
define('PAYOP_IPN_VERSION_V2', 'V2');
define('PAYOP_HASH_ALGORITHM', 'sha256');
define('PAYOP_API_IDENTIFIER', 'identifier');
// Debug mode should only be enabled when WP_DEBUG is true
define('PAYOP_DEBUG_MODE', defined('WP_DEBUG') && WP_DEBUG);

require_once PAYOP_PLUGIN_PATH . '/includes/class-wc-payment-plugin.php';
require_once PAYOP_PLUGIN_PATH . '/includes/payop-debug.php';
require_once PAYOP_PLUGIN_PATH . '/includes/class-wc-payop-payment-method-helper.php';

/**
 * Allow payment methods with IDs that start with 'payop_' to pass validation
 */
function payop_validate_payment_methods($gateways) {
    // Log available payment gateways for debugging
    if (function_exists('payop_debug')) {
        $payop_gateways = array_filter(array_keys($gateways), function($id) {
            return strpos($id, 'payop') === 0;
        });
        payop_debug('Total payment gateways: ' . count($gateways), 'info');
        payop_debug('PayOp payment gateways: ' . implode(', ', $payop_gateways), 'info');
        payop_debug('PayOp gateway count: ' . count($payop_gateways), 'info');
    }

    return $gateways;
}
add_filter('woocommerce_available_payment_gateways', 'payop_validate_payment_methods', 1000);

/**
 * Ensure dynamic payment methods are accepted during checkout processing
 */
add_filter('woocommerce_payment_gateways_validations', function($validations, $order_id = null, $posted_data = array()) {
    if (isset($posted_data['payment_method']) && strpos($posted_data['payment_method'], PAYOP_PAYMENT_GATEWAY_NAME . '_') === 0) {
        // This is a Payop payment method - skip validation
        return array();
    }
    
    return $validations;
}, 10, 3);

// Initialize plugin
new Payop_WC_Payment_Plugin();

// Add debug endpoint for testing payment methods
add_action('wp_ajax_payop_debug_payment_methods', 'payop_debug_payment_methods');
add_action('wp_ajax_nopriv_payop_debug_payment_methods', 'payop_debug_payment_methods');

function payop_debug_payment_methods() {
    if (!current_user_can('manage_options')) {
        wp_die('Unauthorized');
    }

    $gateway = new WC_Gateway_Payop();
    $selected_methods = $gateway->get_selected_payment_methods();

    echo '<h2>PayOp Debug Information</h2>';
    echo '<h3>Selected Payment Methods (' . count($selected_methods) . '):</h3>';
    echo '<pre>' . print_r($selected_methods, true) . '</pre>';

    // Check gateway credentials
    echo '<h3>Gateway Credentials:</h3>';
    echo '<p>JWT Token: ' . (empty($gateway->jwt_token) ? 'MISSING' : 'Present (' . strlen($gateway->jwt_token) . ' chars)') . '</p>';
    echo '<p>Project ID: ' . (empty($gateway->project_id) ? 'MISSING' : $gateway->project_id) . '</p>';
    echo '<p>Gateway Enabled: ' . ($gateway->enabled === 'yes' ? 'Yes' : 'No') . '</p>';

    // Test API call
    if (!empty($gateway->jwt_token) && !empty($gateway->project_id)) {
        echo '<h3>API Test:</h3>';
        $available_methods = $gateway->payment_methods_handler->get_available_payment_methods(
            $gateway->jwt_token,
            $gateway->project_id
        );

        if (isset($available_methods['error'])) {
            echo '<p style="color: red;">API Error: ' . $available_methods['error'] . '</p>';
        } else {
            echo '<p style="color: green;">API Success: Got ' . (is_array($available_methods) ? count($available_methods) : 0) . ' methods</p>';
        }
    }

    // Check all registered payment gateways
    $all_gateways = WC()->payment_gateways->payment_gateways();
    $payop_gateways = array_filter($all_gateways, function($gateway) {
        return strpos($gateway->id, 'payop') === 0;
    });

    echo '<h3>All Registered PayOp Gateways (' . count($payop_gateways) . '):</h3>';
    foreach ($payop_gateways as $gateway) {
        echo '<p><strong>' . $gateway->id . '</strong>: ' . $gateway->title . ' (Enabled: ' . ($gateway->enabled === 'yes' ? 'Yes' : 'No') . ')</p>';
    }

    // Check available payment gateways (filtered)
    $available_gateways = WC()->payment_gateways->get_available_payment_gateways();
    $available_payop_gateways = array_filter($available_gateways, function($gateway) {
        return strpos($gateway->id, 'payop') === 0;
    });

    echo '<h3>Available PayOp Gateways (After Filtering) (' . count($available_payop_gateways) . '):</h3>';
    foreach ($available_payop_gateways as $gateway) {
        echo '<p><strong>' . $gateway->id . '</strong>: ' . $gateway->title . '</p>';
    }

    // Check global payment method configs
    if (isset($GLOBALS['payop_payment_method_configs'])) {
        echo '<h3>Global Payment Method Configs (' . count($GLOBALS['payop_payment_method_configs']) . '):</h3>';
        echo '<pre>' . print_r(array_keys($GLOBALS['payop_payment_method_configs']), true) . '</pre>';
    } else {
        echo '<h3>Global Payment Method Configs: None</h3>';
    }

    // Test creating a dynamic class
    echo '<h3>Dynamic Class Test:</h3>';
    try {
        $test_class_name = 'WC_Gateway_Payop_Method_Test';
        if (!class_exists($test_class_name)) {
            eval("
                class $test_class_name extends WC_Gateway_Payop {
                    public function __construct() {
                        parent::__construct(array('id' => 'payop_test', 'title' => 'Test Method'));
                    }
                }
            ");
            echo '<p style="color: green;">Dynamic class creation: SUCCESS</p>';
        } else {
            echo '<p style="color: blue;">Dynamic class already exists</p>';
        }
    } catch (Exception $e) {
        echo '<p style="color: red;">Dynamic class creation: ERROR - ' . $e->getMessage() . '</p>';
    }

    wp_die();
}
