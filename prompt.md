# PayOp WooCommerce Plugin Development Plan

## Project Overview
Develop a WooCommerce payment gateway plugin that integrates with PayOp's payment aggregator service using their Direct Integration approach. The plugin must provide a seamless checkout experience by dynamically collecting payment method-specific fields and redirecting users directly to payment provider pages, completely bypassing PayOp's hosted checkout.

## Technical Requirements & Constraints
- **PHP Version**: 8.2+
- **WordPress**: Latest stable version
- **WooCommerce**: Latest stable version with block-based checkout support
- **Architecture**: Must support WooCommerce HPOS (High-Performance Order Storage)
- **Checkout Type**: Block-based checkout implementation (https://developer.woocommerce.com/docs/category/cart-and-checkout-blocks/)
- **Payment Gateway API**: Follow WooCommerce Payment Gateway API standards (https://developer.woocommerce.com/docs/features/payments/payment-gateway-api/)

https://developer.woocommerce.com/docs/block-development/cart-and-checkout-blocks/checkout-payment-methods/payment-method-integration/
https://github.com/woocommerce/woocommerce-blocks/tree/trunk/docs/third-party-developers/extensibility

## Integration Approach
Based on the PayOp API documentation analysis showing 122 available payment methods across 5 categories (bank_transfer: 68, cash: 42, ewallet: 10, cards_international: 1, crypto: 1), implement Direct Integration flow:

1. Create invoice using PayOp API
2. Collect payment method-specific fields dynamically
3. Create checkout transaction
4. Redirect to payment provider (not PayOp hosted page)

## User Experience Flow
1. **Cart to Checkout**: User adds products and proceeds to checkout
2. **Initial Data Collection**: Collect only email address initially (minimal friction)
3. **Payment Method Selection**: Display PayOp payment options (individual or grouped)
4. **Dynamic Field Collection**: Show additional required fields based on selected payment method's config.fields from API response
5. **Order Placement**: User completes additional information and clicks "Place Order"
6. **Direct Redirect**: Redirect to final payment provider page with all collected data

## Admin Configuration Requirements
- **Settings Management**: Comprehensive admin panel for plugin configuration
- **Payment Method Control**: Enable/disable individual payment methods from the 122 available options
- **Grouping Capabilities**: 
 - Group by type (bank_transfer, cash, ewallet, cards_international, crypto) 
- admin should be able to customize the groups
 - Group by country (based on 226+ supported countries)
 - Group by currency (EUR, USD, PHP, GBP, CAD, AUD, BRL, DKK)
- Admin should be able to manage/enable/disable payment options from plugin settings page.
- **API Credentials**: Secure storage of PayOp API credentials (Public Key, Secret Key, JWT Token)

## Development Plan Requirements
Create a detailed, systematic, phased development plan that:

1. **Uses Only Factual Information**: Base all technical decisions on the actual PayOp API documentation analysis showing:
 - 122 payment methods with specific field requirements
 - Field types: email (122), name (120), phone (48), document (37), date_of_birth (18), bank_code (14), bank_type (14)
 - Validation patterns for documents, bank types, and country codes
 - Currency and country support matrices
- Do not hard code variables eg. API Base URL, credentials

2. **Addresses Dynamic Field Handling**: Plan for handling varying field requirements across payment methods:
 - Simple methods (email + name only)
 - Complex methods (email + name + phone + document + bank details)
 - Regional variations (European SEPA vs Latin American PSE)

3. **Implements Proper API Integration**: 
 - Signature generation for invoice creation
 - JWT authentication for checkout operations
 - Error handling for all documented response codes
 - IPN (Instant Payment Notification) handling

4. **Ensures WooCommerce Compliance**:
 - Block-based checkout compatibility
 - HPOS support implementation
 - Payment Gateway API adherence
 - Security best practices

5. **Provides Scalable Architecture**: Design for handling 122+ payment methods with different configurations, field requirements, and regional variations

## Output Format
Deliver the plan as a structured markdown (.md) file with clear phases, technical specifications, implementation details, and timeline estimates based on the complexity revealed in the PayOp API analysis.