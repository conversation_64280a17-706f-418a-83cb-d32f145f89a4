/**
 * Payop Payment Methods Checkout
 *
 * Handles payment method selection and field validation at checkout
 */
(function ($) {
  "use strict";

  // Initialize when document is ready
  $(document).ready(function () {
    const payopCheckout = {
      /**
       * Initialize checkout functionality
       */
      init: function () {
        this.bindEvents();
        this.initializeFieldValidation();
      },

      /**
       * Bind event handlers
       */
      bindEvents: function () {
        // Handle payment method selection changes
        $(document.body).on(
          "change",
          '[id^="payment_method_payop_"]',
          this.handlePaymentMethodChange
        );
        $(document.body).on("updated_checkout", this.handleUpdatedCheckout);

        // Handle field validation
        $(document.body).on(
          "input blur",
          ".payop-payment-fields input, .payop-payment-fields select",
          this.handleFieldValidation
        );

        // Additional event for Blocks checkout
        $(document.body).on(
          "click",
          ".wp-block-woocommerce-checkout .wc-block-components-radio-control__option",
          this.handleBlocksPaymentMethodClick
        );

        // Validate before form submission
        $(document.body).on(
          "checkout_place_order",
          this.validatePaymentFields
        );
      },

      /**
       * Initialize field validation setup
       */
      initializeFieldValidation: function () {
        // Add validation styles
        if (!$("#payop-field-validation-styles").length) {
          $("head").append(`
            <style id="payop-field-validation-styles">
              .payop-payment-fields .field-error {
                border-color: #dc3232 !important;
                box-shadow: 0 0 0 1px #dc3232 !important;
              }
              .payop-field-error-message {
                color: #dc3232;
                font-size: 0.85em;
                margin-top: 5px;
                display: block;
              }
              .payop-payment-fields .form-row {
                margin-bottom: 15px;
              }
            </style>
          `);
        }
      },

      /**
       * Handle payment method change event
       */
      handlePaymentMethodChange: function () {
        // Remove selected class from all payment methods
        $('.wc_payment_method[class*="payment_method_payop_"]').removeClass(
          "payop-selected"
        );

        // Add selected class to the checked payment method
        const $selectedMethod = $(
          '.wc_payment_method input[name="payment_method"]:checked'
        ).closest(".wc_payment_method");
        
        if (
          $selectedMethod.attr("class") &&
          $selectedMethod.attr("class").includes("payment_method_payop_")
        ) {
          $selectedMethod.addClass("payop-selected");
          
          // Clear any previous validation errors
          payopCheckout.clearFieldErrors();
        }
      },

      /**
       * Handle updated checkout event
       */
      handleUpdatedCheckout: function () {
        payopCheckout.handlePaymentMethodChange();
      },

      /**
       * Handle payment method clicks in Blocks checkout
       */
      handleBlocksPaymentMethodClick: function (e) {
        const $target = $(e.currentTarget);
        const id = $target.find("input").attr("id");

        if (
          id &&
          id.includes("radio-control-wc-payment-method-options-payop_")
        ) {
          // Add slight delay to allow WC to update the selection
          setTimeout(function () {
            // Check if this is selected
            if ($target.find("input").is(":checked")) {
              // Extract payment method ID from the element ID
              const methodId = id.replace(
                "radio-control-wc-payment-method-options-payop_",
                ""
              );
              console.log("Selected Payop payment method:", methodId);
              
              // Clear any previous validation errors
              payopCheckout.clearFieldErrors();
            }
          }, 50);
        }
      },

      /**
       * Handle field validation
       */
      handleFieldValidation: function (e) {
        const $field = $(e.target);
        const fieldName = $field.attr("name");
        const fieldValue = $field.val();
        const isRequired = $field.attr("required");

        // Clear previous error state
        $field.removeClass("field-error");
        $field.siblings(".payop-field-error-message").remove();

        // Validate required fields
        if (isRequired && (!fieldValue || fieldValue.trim() === "")) {
          payopCheckout.showFieldError($field, "This field is required.");
          return false;
        }

        // Validate email fields
        if ($field.attr("type") === "email" && fieldValue) {
          const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
          if (!emailRegex.test(fieldValue)) {
            payopCheckout.showFieldError($field, "Please enter a valid email address.");
            return false;
          }
        }

        // Validate pattern if specified
        const pattern = $field.attr("pattern");
        if (pattern && fieldValue && !new RegExp(pattern).test(fieldValue)) {
          payopCheckout.showFieldError($field, "Please enter a valid value.");
          return false;
        }

        return true;
      },

      /**
       * Show field error
       */
      showFieldError: function ($field, message) {
        $field.addClass("field-error");
        $field.after(
          '<span class="payop-field-error-message">' + message + "</span>"
        );
      },

      /**
       * Clear all field errors
       */
      clearFieldErrors: function () {
        $(".payop-payment-fields .field-error").removeClass("field-error");
        $(".payop-field-error-message").remove();
      },

      /**
       * Validate payment fields before form submission
       */
      validatePaymentFields: function () {
        const selectedPaymentMethod = $(
          'input[name="payment_method"]:checked'
        ).val();

        // Only validate if a Payop payment method is selected
        if (!selectedPaymentMethod || !selectedPaymentMethod.startsWith("payop_")) {
          return true;
        }

        let isValid = true;
        const $paymentFields = $(
          ".payop-payment-fields input, .payop-payment-fields select"
        );

        // Clear previous errors
        payopCheckout.clearFieldErrors();

        // Validate each field
        $paymentFields.each(function () {
          const $field = $(this);
          const fieldValid = payopCheckout.handleFieldValidation({
            target: $field[0],
          });
          if (!fieldValid) {
            isValid = false;
          }
        });

        if (!isValid) {
          // Scroll to first error field
          const $firstError = $(".payop-payment-fields .field-error").first();
          if ($firstError.length) {
            $("html, body").animate(
              {
                scrollTop: $firstError.offset().top - 100,
              },
              500
            );
          }
        }

        return isValid;
      },
    };

    // Initialize checkout functionality
    payopCheckout.init();
  });
})(jQuery);
