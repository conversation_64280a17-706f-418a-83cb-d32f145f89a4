/**
 * Payop Payment Gateway Blocks Integration
 *
 * Handles integration with WooCommerce Blocks for the Payop payment gateway
 */

// Get payment method settings from the localized data
const settings = window.wc.wcSettings.getSetting("payop_data", {});

// Base gateway name
const baseGatewayName = payopBlockData.name;

// Import required WordPress components
const {registerPaymentMethod} = window.wc.wcBlocksRegistry;
const {decodeEntities} = window.wp.htmlEntities;
const {__} = window.wp.i18n;
const {createElement, Fragment} = window.wp.element;

// Base label and description
const baseLabel =
  decodeEntities(payopBlockData.title) || __("Payop", "payop-woocommerce");
const baseDescription = decodeEntities(payopBlockData.description || "");

// Check if we have selected payment methods
const hasSelectedMethods =
  Array.isArray(payopBlockData.selectedMethods) &&
  payopBlockData.selectedMethods.length > 0;

/**
 * Content component for payment method description
 */
const Content = ({description}) => {
  return createElement("div", {
    dangerouslySetInnerHTML: {__html: description || baseDescription},
  });
};

/**
 * Payment method logo component
 */
const PaymentMethodLogo = ({logo, title}) => {
  if (!logo || !payopBlockData.showLogos) {
    return null;
  }

  return createElement("img", {
    src: logo,
    alt: title,
    className: "payop-payment-method-logo",
  });
};

/**
 * Function to register a single payment method
 */
const registerSinglePaymentMethod = (methodData = null) => {
  // If no method data is provided, register the base gateway
  const name = methodData
    ? `${baseGatewayName}_${methodData.identifier}`
    : baseGatewayName;
  const label = methodData ? methodData.title : baseLabel;

  const paymentMethod = {
    name,
    label,
    content: createElement(Content, {
      description: methodData ? null : baseDescription,
    }),
    edit: createElement(Content, {
      description: methodData ? null : baseDescription,
    }),
    canMakePayment: () => true,
    ariaLabel: label,
    supports: {
      features: ["products"],
    },
  };

  // If we have method-specific data, add logo and payment method ID
  if (methodData) {
    // Add logo to label if available
    if (methodData.logo && payopBlockData.showLogos) {
      paymentMethod.label = createElement(
        Fragment,
        {},
        createElement(PaymentMethodLogo, {
          logo: methodData.logo,
          title: methodData.title,
        }),
        label
      );
    }

    // Add payment method data for processing
    paymentMethod.data = {
      payopPaymentMethod: methodData.identifier,
    };
  }

  // Register the payment method
  registerPaymentMethod(paymentMethod);
};

// Register payment methods
if (hasSelectedMethods) {
  // Register each selected payment method individually
  payopBlockData.selectedMethods.forEach(methodData => {
    registerSinglePaymentMethod(methodData);
  });
} else {
  // Register the base gateway if no selected methods
  registerSinglePaymentMethod();
}
