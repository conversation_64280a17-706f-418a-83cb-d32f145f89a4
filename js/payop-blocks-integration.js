/**
 * Payop Payment Gateway Blocks Integration
 *
 * Handles integration with WooCommerce Blocks for the Payop payment gateway
 */

// Get payment method settings from the localized data
const settings = window.wc.wcSettings.getSetting("payop_data", {});

// Base gateway name
const baseGatewayName = payopBlockData.name;

// Import required WordPress components
const {registerPaymentMethod} = window.wc.wcBlocksRegistry;
const {decodeEntities} = window.wp.htmlEntities;
const {__} = window.wp.i18n;
const {createElement, Fragment, useState, useEffect} = window.wp.element;

// Base label and description
const baseLabel =
  decodeEntities(payopBlockData.title) || __("Payop", "payop-woocommerce");
const baseDescription = decodeEntities(payopBlockData.description || "");

// Check if we have selected payment methods
const hasSelectedMethods =
  Array.isArray(payopBlockData.selectedMethods) &&
  payopBlockData.selectedMethods.length > 0;

/**
 * Content component for payment method description
 */
const Content = ({description}) => {
  return createElement("div", {
    dangerouslySetInnerHTML: {__html: description || baseDescription},
  });
};

/**
 * Payment method logo component
 */
const PaymentMethodLogo = ({logo, title}) => {
  if (!logo || !payopBlockData.showLogos) {
    return null;
  }

  return createElement("img", {
    src: logo,
    alt: title,
    className: "payop-payment-method-logo",
  });
};

/**
 * Dynamic fields component for payment methods
 */
const DynamicFields = ({methodId, onFieldChange}) => {
  const [fields, setFields] = useState([]);
  const [loading, setLoading] = useState(false);
  const [fieldValues, setFieldValues] = useState({});

  // Load fields when method ID changes
  useEffect(() => {
    if (!methodId) {
      setFields([]);
      return;
    }

    setLoading(true);

    // Fetch payment method fields via AJAX
    const formData = new FormData();
    formData.append('action', 'payop_get_payment_method_fields');
    formData.append('security', payopBlockData.nonce);
    formData.append('payment_method_id', methodId);
    formData.append('jwt_token', payopBlockData.jwtToken);
    formData.append('project_id', payopBlockData.projectId);

    fetch(payopBlockData.ajaxUrl, {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      if (data.success && data.data) {
        setFields(data.data);
      } else {
        setFields([]);
        console.error('Failed to load payment method fields:', data.data?.message || 'Unknown error');
      }
    })
    .catch(error => {
      console.error('Error loading payment method fields:', error);
      setFields([]);
    })
    .finally(() => {
      setLoading(false);
    });
  }, [methodId]);

  // Handle field value changes
  const handleFieldChange = (fieldName, value) => {
    const newValues = {
      ...fieldValues,
      [fieldName]: value
    };
    setFieldValues(newValues);

    // Notify parent component
    if (onFieldChange) {
      onFieldChange(newValues);
    }
  };

  if (loading) {
    return createElement('div', {
      className: 'payop-loading-fields'
    }, __('Loading payment fields...', 'payop-woocommerce'));
  }

  if (fields.length === 0) {
    return null;
  }

  return createElement('div', {
    className: 'payop-dynamic-fields'
  }, fields.map((field, index) =>
    createElement('div', {
      key: index,
      className: 'payop-field-wrapper'
    }, [
      createElement('label', {
        key: 'label',
        htmlFor: `payop_field_${field.name}`,
        className: 'payop-field-label'
      }, [
        field.label,
        field.required && createElement('span', {
          key: 'required',
          className: 'required'
        }, ' *')
      ]),
      createElement('input', {
        key: 'input',
        type: field.type || 'text',
        id: `payop_field_${field.name}`,
        name: `payop_field_${field.name}`,
        value: fieldValues[field.name] || '',
        onChange: (e) => handleFieldChange(field.name, e.target.value),
        required: field.required,
        placeholder: field.placeholder || '',
        className: 'payop-field-input'
      })
    ])
  ));
};

/**
 * Function to register a single payment method
 */
const registerSinglePaymentMethod = (methodData = null) => {
  // If no method data is provided, register the base gateway
  const name = methodData
    ? `${baseGatewayName}_${methodData.identifier}`
    : baseGatewayName;
  const label = methodData ? methodData.title : baseLabel;

  const paymentMethod = {
    name,
    label,
    content: createElement(Content, {
      description: methodData ? null : baseDescription,
    }),
    edit: createElement(Content, {
      description: methodData ? null : baseDescription,
    }),
    canMakePayment: () => true,
    ariaLabel: label,
    supports: {
      features: ["products"],
    },
  };

  // If we have method-specific data, add logo and payment method ID
  if (methodData) {
    // Add logo to label if available
    if (methodData.logo && payopBlockData.showLogos) {
      paymentMethod.label = createElement(
        Fragment,
        {},
        createElement(PaymentMethodLogo, {
          logo: methodData.logo,
          title: methodData.title,
        }),
        label
      );
    }

    // Add payment method data for processing
    paymentMethod.data = {
      payopPaymentMethod: methodData.identifier,
    };
  }

  // Register the payment method
  registerPaymentMethod(paymentMethod);
};

// Register payment methods
if (hasSelectedMethods) {
  // Register each selected payment method individually
  payopBlockData.selectedMethods.forEach(methodData => {
    registerSinglePaymentMethod(methodData);
  });
} else {
  // Register the base gateway if no selected methods
  registerSinglePaymentMethod();
}
