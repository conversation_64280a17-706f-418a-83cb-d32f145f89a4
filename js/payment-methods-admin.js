/**
 * Payop Payment Methods Admin
 *
 * Handles payment methods management in the admin interface
 */
(function ($) {
  "use strict";

  // Initialize when document is ready
  $(document).ready(function () {
    const payopAdmin = {
      /**
       * Initialize admin functionality
       */
      init: function () {
        this.bindEvents();
        this.updateSelectedCount();

        // Log initial state to help with debugging
        console.log("Payop Payment Methods Admin initialized");
      },

      /**
       * Bind event handlers
       */
      bindEvents: function () {
        $("#payop-refresh-methods").on("click", this.refreshMethods);
        $("#payop-select-all-methods").on("click", this.selectAllMethods);
        $("#payop-deselect-all-methods").on("click", this.deselectAllMethods);
        $("#payop-save-methods").on("click", this.saveSelectedMethods);
        $("#payop-payment-methods-list").on(
          "change",
          'input[type="checkbox"]',
          this.handleCheckboxChange
        );

        // Initialize sortable functionality
        this.initSortable();
      },

      /**
       * Initialize sortable list
       */
      initSortable: function () {
        if ($("#payop-payment-methods-list").length) {
          $("#payop-payment-methods-list").sortable({
            handle: ".payop-method-handle",
            update: function () {
              payopAdmin.updateSelectedCount();
              $("#payop-save-methods").prop("disabled", false);
            },
          });
        }
      },

      /**
       * Handle checkbox change
       */
      handleCheckboxChange: function () {
        payopAdmin.updateSelectedCount();
        $("#payop-save-methods").prop("disabled", false);
      },

      /**
       * Update selected count display
       */
      updateSelectedCount: function () {
        const totalMethods = $("#payop-payment-methods-list li").length;
        const selectedMethods = $(
          '#payop-payment-methods-list input[type="checkbox"]:checked'
        ).length;
        $(".payop-selected-count").text(
          selectedMethods + " of " + totalMethods
        );
      },

      /**
       * Refresh payment methods from API
       */
      refreshMethods: function (e) {
        e.preventDefault();

        // Get credentials from form fields
        const jwt_token = $("#woocommerce_payop_jwt_token").val();
        const project_id = $("#woocommerce_payop_project_id").val();

        console.log(
          "Refreshing payment methods with JWT token and Project ID:",
          {
            jwtToken: jwt_token ? "Provided" : "Missing",
            projectId: project_id,
          }
        );

        // Check if credentials are provided
        if (!jwt_token || !project_id) {
          payopAdmin.showNotice("error", payop_admin_i18n.missing_credentials);
          return;
        }

        // Show spinner
        $(".payop-spinner").css("display", "inline-block");

        // Clear any existing notices
        $(".payop-admin-notice")
          .removeClass("notice-error notice-success")
          .hide()
          .html("");

        // Make AJAX request
        $.ajax({
          url: payop_admin_i18n.ajax_url,
          type: "POST",
          data: {
            action: "payop_refresh_payment_methods",
            nonce: payop_admin_i18n.nonce,
            jwt_token: jwt_token,
            project_id: project_id,
          },
          success: function (response) {
            console.log("Payment methods refresh response:", response);
            $(".payop-spinner").hide();

            if (response.success && response.data && response.data.methods) {
              // Show success message
              payopAdmin.showNotice(
                "success",
                response.data.message || payop_admin_i18n.refresh_success
              );

              // Render payment methods
              payopAdmin.renderPaymentMethods(response.data.methods);

              // Initialize sortable again
              payopAdmin.initSortable();

              // Update selected count
              payopAdmin.updateSelectedCount();

              // Enable save button
              $("#payop-save-methods").prop("disabled", false);
            } else {
              // Show error message
              const errorMessage =
                response.data && response.data.message
                  ? response.data.message
                  : payop_admin_i18n.ajax_error;

              payopAdmin.showNotice("error", errorMessage);
            }
          },
          error: function (xhr, status, error) {
            $(".payop-spinner").hide();
            console.error("Payop API Error:", status, error);
            console.error("Response:", xhr.responseText);
            payopAdmin.showNotice(
              "error",
              payop_admin_i18n.ajax_error + ": " + error
            );
          },
        });
      },

      /**
       * Render payment methods
       *
       * @param {Array} methods Payment methods to render
       */
      renderPaymentMethods: function (methods) {
        const $list = $("#payop-payment-methods-list");

        // Clear list
        $list.empty();

        if (!methods || methods.length === 0) {
          $list.html(
            '<p class="payop-payment-methods-empty">' +
              payop_admin_i18n.no_methods +
              "</p>"
          );
          return;
        }

        console.log("Rendering payment methods:", methods.length);

        // Get currently selected methods
        const selectedMethods = $(
          'input[name="woocommerce_payop_selected_payment_methods"]'
        ).val();
        const selectedIds = selectedMethods ? selectedMethods.split(",") : [];

        // Render each method
        $.each(methods, function (index, method) {
          // Use pmIdentifier if identifier is not available
          const methodId = method.identifier || method.pmIdentifier;
          const isSelected = $.inArray(methodId.toString(), selectedIds) !== -1;

          // Handle currencies and countries arrays
          const currencies =
            method.currencies && method.currencies.length
              ? method.currencies.join(", ")
              : "";
          const countries =
            method.countries && method.countries.length
              ? method.countries.join(", ")
              : "";

          const $item = $(
            '<li class="payop-method-item" data-id="' +
              methodId +
              '">' +
              '<div class="payop-method-handle dashicons dashicons-menu"></div>' +
              '<div class="payop-method-checkbox">' +
              '<input type="checkbox" id="payop-method-' +
              methodId +
              '" ' +
              'name="payop_selected_methods[]" ' +
              'value="' +
              methodId +
              '" ' +
              (isSelected ? "checked" : "") +
              ">" +
              "</div>" +
              '<div class="payop-method-logo">' +
              '<img src="' +
              (method.logo || "") +
              '" alt="' +
              method.title +
              '">' +
              "</div>" +
              '<div class="payop-method-details">' +
              '<label for="payop-method-' +
              methodId +
              '" class="payop-method-title">' +
              method.title +
              "</label>" +
              '<div class="payop-method-meta">' +
              '<span class="payop-method-id">ID: ' +
              methodId +
              "</span>" +
              (currencies
                ? '<span class="payop-method-currencies">Currencies: ' +
                  currencies +
                  "</span>"
                : "") +
              (countries
                ? '<span class="payop-method-countries">Countries: ' +
                  countries +
                  "</span>"
                : "") +
              "</div>" +
              "</div>" +
              "</li>"
          );

          $list.append($item);
        });
      },

      /**
       * Select all payment methods
       */
      selectAllMethods: function (e) {
        e.preventDefault();
        $('#payop-payment-methods-list input[type="checkbox"]').prop(
          "checked",
          true
        );
        payopAdmin.updateSelectedCount();
        $("#payop-save-methods").prop("disabled", false);
      },

      /**
       * Deselect all payment methods
       */
      deselectAllMethods: function (e) {
        e.preventDefault();
        $('#payop-payment-methods-list input[type="checkbox"]').prop(
          "checked",
          false
        );
        payopAdmin.updateSelectedCount();
        $("#payop-save-methods").prop("disabled", false);
      },

      /**
       * Save selected payment methods
       */
      saveSelectedMethods: function (e) {
        e.preventDefault();

        // Get selected method IDs in the current order
        const selectedIds = [];
        $("#payop-payment-methods-list li").each(function () {
          const $item = $(this);
          if ($item.find('input[type="checkbox"]').is(":checked")) {
            selectedIds.push($item.data("id"));
          }
        });

        console.log("Saving selected payment methods:", selectedIds);

        // Update hidden field with comma-separated list of IDs
        $('input[name="woocommerce_payop_selected_payment_methods"]').val(
          selectedIds.join(",")
        );

        // Show success message
        payopAdmin.showNotice("success", payop_admin_i18n.save_success);

        // Disable save button
        $("#payop-save-methods").prop("disabled", true);
      },

      /**
       * Show a notice message
       *
       * @param {string} type Notice type (success, error)
       * @param {string} message Message to display
       */
      showNotice: function (type, message) {
        const $notice = $(".payop-admin-notice");
        $notice
          .removeClass("notice-success notice-error")
          .addClass("notice-" + type)
          .html(message)
          .show();

        // Auto-hide success messages after 5 seconds
        if (type === "success") {
          setTimeout(function () {
            $notice.fadeOut();
          }, 5000);
        }
      },
    };

    // Initialize admin functionality
    payopAdmin.init();
  });
})(jQuery);
