<?php
/**
 * Settings for Payop Standard Gateway.
 *
 * @version 1.1.0
 */

if (!defined('ABSPATH')) {
	exit;
}

return [
	'enabled' => [
		'title' => __('Enable Payop payments', 'payop-woocommerce'),
		'type' => 'checkbox',
		'label' => __('Enable/Disable', 'payop-woocommerce'),
		'default' => 'yes',
	],

	'title' => [
		'title' => __('Name of payment gateway', 'payop-woocommerce'),
		'type' => 'text',
		'description' => __('The name of the payment gateway that the user see when placing the order', 'payop-woocommerce'),
		'default' => __('Payop', 'payop-woocommerce'),
	],

	'description' => [
		'title' => __('Description', 'payop-woocommerce'),
		'type' => 'textarea',
		'description' => __('Description of the payment gateway that the client will see on your site.', 'payop-woocommerce'),
		'default' => __('Accept online payments using Payop.com', 'payop-woocommerce'),
	],

	'public_key' => [
		'title' => __('Public key', 'payop-woocommerce'),
		'type' => 'text',
		'description' => __('Issued in the client panel https://payop.com', 'payop-woocommerce'),
		'default' => '',
	],

	'secret_key' => [
		'title' => __('Secret key', 'payop-woocommerce'),
		'type' => 'text',
		'description' => __('Issued in the client panel https://payop.com', 'payop-woocommerce'),
		'default' => '',
	],

	'project_id' => [
		'title' => __('Project ID', 'payop-woocommerce'),
		'type' => 'text',
		'description' => __('Your project ID from Payop dashboard. You can find it in the Projects → Project List → Details section.', 'payop-woocommerce'),
		'default' => '',
	],

	'ipn_url' => [
		'title'       => __('Callback / IPN URL', 'payop-woocommerce'),
		'type'        => 'text',
		'description' => __('Copy this URL and paste it in your Payop project settings (IPN section)', 'payop-woocommerce'),
		'default'     => add_query_arg([
			'wc-api' => 'wc_payop',
			'payop'  => 'result',
		], home_url('/')),
		'custom_attributes' => [
			'readonly' => 'readonly',
			'onclick' => "this.select();",
		],
	],

	'payment_methods_title' => [
		'title' => __('Payment Methods', 'payop-woocommerce'),
		'type' => 'title',
		'description' => __('Configure which payment methods are available to your customers. Selected payment methods will appear as individual options at checkout.', 'payop-woocommerce'),
	],

	'payment_methods_manager' => [
		'type' => 'payment_methods_manager',
		'description' => __('Select and arrange payment methods to display at checkout.', 'payop-woocommerce'),
	],

	'selected_payment_methods' => [
		'type' => 'hidden',
		'default' => '',
	],

	'show_payment_logos' => [
		'title' => __('Show payment method logos', 'payop-woocommerce'),
		'type' => 'checkbox',
		'label' => __('Display payment method logos at checkout', 'payop-woocommerce'),
		'default' => 'yes',
	],

	'payment_options_title' => [
		'title' => __('Payment Options', 'payop-woocommerce'),
		'type' => 'title',
	],

	'auto_complete' => [
		'title' => __('Order completion', 'payop-woocommerce'),
		'type' => 'checkbox',
		'label' => __('Automatic transfer of the order to the status "Completed" after successful payment', 'payop-woocommerce'),
		'default' => '1',
	],

	'skip_confirm' => [
		'title' => __('Skip confirmation', 'payop-woocommerce'),
		'type' => 'checkbox',
		'label' => __('Skip page checkout confirmation', 'payop-woocommerce'),
		'default' => 'yes',
	],

	'direct_redirect' => [
		'title' => __('Direct payment method redirect', 'payop-woocommerce'),
		'type' => 'checkbox',
		'label' => __('Redirect directly to the payment method page instead of showing the Payop checkout page', 'payop-woocommerce'),
		'default' => 'yes',
	],
];
