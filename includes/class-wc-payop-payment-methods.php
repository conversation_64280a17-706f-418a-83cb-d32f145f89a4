<?php
/**
 * Payop Payment Methods Handler
 *
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
	exit;
}

/**
 * Class WC_Payop_Payment_Methods
 *
 * Handles retrieval, storage, and management of Payop payment methods
 */
class WC_Payop_Payment_Methods {
	/**
	 * API endpoint for retrieving available payment methods
	 *
	 * @var string
	 */
	private $payment_methods_endpoint = 'https://api.payop.com/v1/instrument-settings/payment-methods/available-for-application';
	
	/**
	 * API base URL
	 *
	 * @var string
	 */
	private $api_base_url = 'https://api.payop.com/v1';
	
	/**
	 * Option name for storing payment methods in WordPress database
	 *
	 * @var string
	 */
	private $payment_methods_option = 'payop_available_payment_methods';
	
	/**
	 * Option name for storing selected payment methods in WordPress database
	 *
	 * @var string
	 */
	private $selected_methods_option = 'payop_selected_payment_methods';
	
	/**
	 * Cache expiry time in seconds (24 hours)
	 *
	 * @var int
	 */
	private $cache_expiry = 86400;
	
	/**
	 * Constructor
	 */
	public function __construct() {
		// Add AJAX handlers for admin
		add_action('wp_ajax_payop_refresh_payment_methods', array($this, 'ajax_refresh_payment_methods'));
	}
	
	/**
	 * Get available payment methods from API or cache
	 *
	 * @param string $jwt_token The JWT token for authentication
	 * @param string $project_id The project ID for which to retrieve payment methods
	 * @param bool $force_refresh Whether to bypass cache and force a refresh from the API
	 * @return array Payment methods array or error
	 */
	public function get_available_payment_methods($jwt_token, $project_id, $force_refresh = false) {
		if (empty($jwt_token) || empty($project_id)) {
			return array(
				'error' => __('Missing credentials. Please provide JWT token and project ID.', 'payop-woocommerce')
			);
		}
		
		// Get cached payment methods
		$stored_data = get_option($this->payment_methods_option, false);
		$now = time();
		
		// Return cached data if not expired and not forcing refresh
		if (!$force_refresh && $stored_data && 
		    isset($stored_data['expires']) && $stored_data['expires'] > $now &&
		    isset($stored_data['project_id']) && $stored_data['project_id'] === $project_id &&
		    !empty($stored_data['methods'])) {
			return $stored_data['methods'];
		}
		
		// Get fresh data from API
		$response = $this->fetch_payment_methods_from_api($jwt_token, $project_id);
		
		if (isset($response['error'])) {
			return $response;
		}
		
		// Save to cache
		$cache_data = array(
			'methods' => $response,
			'project_id' => $project_id,
			'expires' => $now + $this->cache_expiry
		);
		
		update_option($this->payment_methods_option, $cache_data);
		
		return $response;
	}
	
	/**
	 * Fetch payment methods directly from the Payop API
	 *
	 * @param string $jwt_token The JWT token for authentication
	 * @param string $project_id The project ID
	 * @return array Payment methods or error
	 */
	private function fetch_payment_methods_from_api($jwt_token, $project_id) {
		// Request URL with trailing slash
		$request_url = trailingslashit($this->payment_methods_endpoint) . $project_id;
		
		// Debug request URL and data being sent
		error_log('Payop Payment Methods API Request URL: ' . $request_url);
		
		$args = array(
			'headers' => array(
				'Authorization' => 'Bearer ' . $jwt_token,
				'Content-Type' => 'application/json'
			),
			'timeout' => 45,
			'sslverify' => true
		);
		
		$response = wp_remote_get($request_url, $args);
		
		// Check for errors
		if (is_wp_error($response)) {
			error_log('Payop Payment Methods API Error: ' . $response->get_error_message());
			return array(
				'error' => $response->get_error_message()
			);
		}
		
		$response_code = wp_remote_retrieve_response_code($response);
		$response_body = wp_remote_retrieve_body($response);
		
		// Debug the response
		error_log('Payop Payment Methods API Response Code: ' . $response_code);
		error_log('Payop Payment Methods API Response Body: ' . $response_body);
		
		$response_data = json_decode($response_body, true);
		
		// Handle different error cases with specific messages
		if ($response_code === 401) {
			return array(
				'error' => __('Authentication failed. Please check your JWT token and try again.', 'payop-woocommerce')
			);
		} elseif ($response_code === 403) {
			return array(
				'error' => __('Access denied. Your JWT token may not have sufficient permissions.', 'payop-woocommerce')
			);
		} elseif ($response_code === 404) {
			return array(
				'error' => __('Project ID not found. Please verify your Project ID is correct.', 'payop-woocommerce')
			);
		} elseif ($response_code !== 200 || !is_array($response_data)) {
			$error_message = isset($response_data['message']) ? 
				$response_data['message'] : 
				__('Failed to retrieve payment methods. Please check your credentials.', 'payop-woocommerce');
				
			return array(
				'error' => $error_message
			);
		}
		
		// According to documentation, payment methods are in the 'data' array
		if (!isset($response_data['data']) || !is_array($response_data['data'])) {
			return array(
				'error' => __('Invalid response format. Could not find payment methods data.', 'payop-woocommerce')
			);
		}
		
		return $response_data['data'];
	}
	
	/**
	 * AJAX handler to refresh payment methods from API
	 */
	public function ajax_refresh_payment_methods() {
		// Check permissions
		if (!current_user_can('manage_woocommerce')) {
			wp_send_json_error(array('message' => __('You do not have sufficient permissions to perform this action.', 'payop-woocommerce')));
		}
		
		// Check nonce
		if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'payop_refresh_payment_methods')) {
			wp_send_json_error(array('message' => __('Security check failed.', 'payop-woocommerce')));
		}
		
		// Get credentials
		$jwt_token = isset($_POST['jwt_token']) ? sanitize_text_field($_POST['jwt_token']) : '';
		$project_id = isset($_POST['project_id']) ? sanitize_text_field($_POST['project_id']) : '';
		
		if (empty($jwt_token) || empty($project_id)) {
			wp_send_json_error(array('message' => __('Missing JWT token or Project ID. Please provide both credentials.', 'payop-woocommerce')));
			return;
		}
		
		// Force refresh from API by setting third parameter to true
		$payment_methods = $this->get_available_payment_methods($jwt_token, $project_id, true);
		
		if (isset($payment_methods['error'])) {
			wp_send_json_error(array('message' => $payment_methods['error']));
			return;
		}
		
		wp_send_json_success(array(
			'methods' => $payment_methods,
			'message' => __('Payment methods refreshed successfully.', 'payop-woocommerce')
		));
	}
	
	/**
	 * Get selected payment methods
	 *
	 * @return array Array of selected payment method IDs
	 */
	public function get_selected_payment_methods() {
		$selected_methods = get_option($this->selected_methods_option, array());
		
		if (!is_array($selected_methods)) {
			$selected_methods = array();
		}
		
		return $selected_methods;
	}
	
	/**
	 * Update selected payment methods
	 *
	 * @param array $method_ids Array of payment method IDs to select
	 * @return bool Success status
	 */
	public function update_selected_payment_methods($method_ids) {
		if (!is_array($method_ids)) {
			return false;
		}
		
		// Sanitize array of IDs
		$method_ids = array_map('sanitize_text_field', $method_ids);
		
		return update_option($this->selected_methods_option, $method_ids);
	}
	
	/**
	 * Get payment method by identifier
	 *
	 * @param string|int $identifier The payment method identifier
	 * @param array $methods Optional array of payment methods to search in
	 * @return array|bool Payment method data or false if not found
	 */
	public function get_payment_method_by_id($identifier, $methods = null) {
		if ($methods === null) {
			// Get gateway instance
			$gateway = WC()->payment_gateways->payment_gateways()[PAYOP_PAYMENT_GATEWAY_NAME];
			
			// Get methods from cache or API
			$methods = $this->get_available_payment_methods(
				$gateway->jwt_token, 
				$gateway->project_id
			);
			
			if (isset($methods['error'])) {
				return false;
			}
		}
		
		foreach ($methods as $method) {
			if ((isset($method['identifier']) && $method['identifier'] == $identifier) || 
			    (isset($method['pmIdentifier']) && $method['pmIdentifier'] == $identifier)) {
				return $method;
			}
		}
		
		return false;
	}
	
	/**
	 * Get required fields for a specific payment method
	 *
	 * @param string $jwt_token The JWT token for authentication
	 * @param string $project_id The project ID
	 * @param string $payment_method_id The payment method identifier
	 * @return array Required fields array or error
	 */
	public function get_payment_method_required_fields($jwt_token, $project_id, $payment_method_id) {
		if (empty($jwt_token) || empty($project_id) || empty($payment_method_id)) {
			return array(
				'error' => __('Missing required parameters for fetching payment method fields.', 'payop-woocommerce')
			);
		}
		
		// Check cache first
		$cache_key = 'payop_pm_fields_' . $project_id . '_' . $payment_method_id;
		$cached_fields = get_transient($cache_key);
		
		if ($cached_fields !== false) {
			return $cached_fields;
		}
		
		// Fetch from API
		$endpoint = $this->api_base_url . '/instrument-settings/payment-methods/available-for-application/' . $project_id;
		
		$args = array(
			'headers' => array(
				'Authorization' => 'Bearer ' . $jwt_token,
				'Content-Type' => 'application/json'
			),
			'timeout' => 30,
			'sslverify' => true
		);
		
		$response = wp_remote_get($endpoint, $args);
		
		if (is_wp_error($response)) {
			return array(
				'error' => $response->get_error_message()
			);
		}
		
		$response_code = wp_remote_retrieve_response_code($response);
		$response_body = wp_remote_retrieve_body($response);
		$response_data = json_decode($response_body, true);
		
		if ($response_code !== 200 || !is_array($response_data)) {
			return array(
				'error' => __('Failed to retrieve payment methods from API.', 'payop-woocommerce')
			);
		}
		
		// Find the specific payment method and extract all available fields
		$required_fields = array();
		
		if (isset($response_data['data']) && is_array($response_data['data'])) {
			foreach ($response_data['data'] as $method) {
				$method_identifier = isset($method['identifier']) ? $method['identifier'] : null;
				if ($method_identifier === $payment_method_id) {
					if (isset($method['config']['fields']) && is_array($method['config']['fields'])) {
						foreach ($method['config']['fields'] as $field) {
							// Include ALL fields defined by the payment method
							$field_info = array(
								'name' => $field['name'],
								'type' => isset($field['type']) ? $field['type'] : 'text',
								'label' => isset($field['title']) ? $field['title'] : ucfirst(str_replace('_', ' ', $field['name'])),
								'required' => isset($field['required']) ? $field['required'] : false,
								'placeholder' => isset($field['placeholder']) ? $field['placeholder'] : '',
								'pattern' => isset($field['pattern']) ? $field['pattern'] : '',
								'maxlength' => isset($field['maxlength']) ? $field['maxlength'] : '',
								'minlength' => isset($field['minlength']) ? $field['minlength'] : '',
								'options' => isset($field['options']) ? $field['options'] : null
							);
							
							// Add specific validation patterns and labels for known field types
							if ($field['name'] === 'document' || $field['name'] === 'cpf') {
								$field_info['pattern'] = $field_info['pattern'] ?: '\d{11}';
								$field_info['placeholder'] = $field_info['placeholder'] ?: '000.000.000-00';
								$field_info['label'] = $field_info['label'] ?: __('CPF', 'payop-woocommerce');
							} elseif ($field['name'] === 'cnpj') {
								$field_info['pattern'] = $field_info['pattern'] ?: '\d{14}';
								$field_info['placeholder'] = $field_info['placeholder'] ?: '00.000.000/0000-00';
								$field_info['label'] = $field_info['label'] ?: __('CNPJ', 'payop-woocommerce');
							} elseif ($field['name'] === 'nationalid') {
								$field_info['label'] = $field_info['label'] ?: __('National ID', 'payop-woocommerce');
							} elseif ($field['name'] === 'email') {
								$field_info['type'] = 'email';
								$field_info['label'] = $field_info['label'] ?: __('Email', 'payop-woocommerce');
							} elseif ($field['name'] === 'name') {
								$field_info['label'] = $field_info['label'] ?: __('Full Name', 'payop-woocommerce');
							}
							
							$required_fields[] = $field_info;
						}
					}
					
					// Log what currency this payment method supports
					if (function_exists('payop_debug')) {
						$currencies = isset($method['currencies']) ? implode(', ', $method['currencies']) : 'Unknown';
						payop_debug("Payment method '{$method['title']}' (ID: $payment_method_id) supports currencies: $currencies", 'info');
					}
					
					break;
				}
			}
		}
		
		// Cache for 1 hour
		set_transient($cache_key, $required_fields, 3600);
		
		return $required_fields;
	}

	/**
	 * Create invoice via Payop API
	 *
	 * @param string $jwt_token JWT token for authentication
	 * @param array $invoice_data Invoice data
	 * @return array Invoice response or error
	 */
	public function create_invoice($jwt_token, $invoice_data) {
		if (empty($jwt_token) || empty($invoice_data)) {
			return array('error' => __('Missing required parameters for invoice creation.', 'payop-woocommerce'));
		}
		
		$endpoint = 'https://api.payop.com/v1/invoices/create';
		
		// Debug: Log the invoice data being sent
		if (function_exists('payop_debug')) {
			payop_debug('Creating invoice with data: ' . wp_json_encode($invoice_data), 'info');
		}
		
		$headers = array(
			'Content-Type' => 'application/json'
		);

		// Add JWT token for direct integration (required for checkout operations)
		if (!empty($jwt_token)) {
			$headers['Authorization'] = 'Bearer ' . $jwt_token;
		}

		$args = array(
			'method' => 'POST',
			'headers' => $headers,
			'body' => wp_json_encode($invoice_data),
			'timeout' => 30,
			'sslverify' => true
		);
		
		$response = wp_remote_request($endpoint, $args);
		
		if (is_wp_error($response)) {
			if (function_exists('payop_debug')) {
				payop_debug('Invoice creation WP error: ' . $response->get_error_message(), 'error');
			}
			return array('error' => $response->get_error_message());
		}
		
		$response_code = wp_remote_retrieve_response_code($response);
		$response_body = wp_remote_retrieve_body($response);
		$response_data = json_decode($response_body, true);
		
		// Debug: Log the response
		if (function_exists('payop_debug')) {
			payop_debug("Invoice creation response - Code: $response_code, Body: $response_body", $response_code === 200 ? 'info' : 'error');
		}
		
		if ($response_code !== 200 || !is_array($response_data)) {
			$error_message = isset($response_data['message']) ? $response_data['message'] : __('Failed to create invoice.', 'payop-woocommerce');
			if (function_exists('payop_debug')) {
				payop_debug("Invoice creation failed - Error: $error_message", 'error');
			}
			return array('error' => $error_message);
		}
		
		return $response_data;
	}

	/**
	 * Create checkout transaction via Payop API
	 *
	 * @param string $jwt_token JWT token for authentication
	 * @param array $transaction_data Transaction data
	 * @return array Transaction response or error
	 */
	public function create_checkout_transaction($jwt_token, $transaction_data) {
		if (empty($jwt_token) || empty($transaction_data)) {
			return array('error' => __('Missing required parameters for transaction creation.', 'payop-woocommerce'));
		}
		
		if (function_exists('payop_debug')) {
			payop_debug('Creating checkout transaction with data: ' . print_r($transaction_data, true), 'info');
		}
		
		$endpoint = 'https://api.payop.com/v1/checkout/create';
		
		$args = array(
			'method' => 'POST',
			'headers' => array(
				'Authorization' => 'Bearer ' . $jwt_token,
				'Content-Type' => 'application/json'
			),
			'body' => wp_json_encode($transaction_data),
			'timeout' => 30,
			'sslverify' => true
		);
		
		$response = wp_remote_request($endpoint, $args);
		
		if (is_wp_error($response)) {
			$error_message = $response->get_error_message();
			if (function_exists('payop_debug')) {
				payop_debug('Checkout transaction API error: ' . $error_message, 'error');
			}
			return array('error' => $error_message);
		}
		
		$response_code = wp_remote_retrieve_response_code($response);
		$response_body = wp_remote_retrieve_body($response);
		$response_data = json_decode($response_body, true);
		
		if (function_exists('payop_debug')) {
			payop_debug('Checkout transaction API response code: ' . $response_code, 'info');
			payop_debug('Checkout transaction API response body: ' . $response_body, 'info');
		}
		
		if ($response_code !== 200 || !is_array($response_data)) {
			$error_message = isset($response_data['message']) ? $response_data['message'] : __('Failed to create checkout transaction.', 'payop-woocommerce');
			if (function_exists('payop_debug')) {
				payop_debug('Checkout transaction failed: ' . $error_message, 'error');
			}
			return array('error' => $error_message);
		}
		
		if (function_exists('payop_debug')) {
			payop_debug('Checkout transaction successful: ' . print_r($response_data, true), 'info');
		}
		
		return $response_data;
	}

	/**
	 * Check transaction status via Payop API
	 *
	 * @param string $jwt_token JWT token for authentication
	 * @param string $invoice_id Invoice ID (not transaction ID)
	 * @return array Transaction status response or error
	 */
	public function check_transaction_status($jwt_token, $invoice_id) {
		if (empty($jwt_token) || empty($invoice_id)) {
			return array('error' => __('Missing required parameters for status check.', 'payop-woocommerce'));
		}

		$endpoint = 'https://api.payop.com/v1/checkout/check-invoice-status/' . $invoice_id;
		
		$args = array(
			'headers' => array(
				'Authorization' => 'Bearer ' . $jwt_token,
				'Content-Type' => 'application/json'
			),
			'timeout' => 30,
			'sslverify' => true
		);
		
		$response = wp_remote_get($endpoint, $args);
		
		if (is_wp_error($response)) {
			return array('error' => $response->get_error_message());
		}
		
		$response_code = wp_remote_retrieve_response_code($response);
		$response_body = wp_remote_retrieve_body($response);
		$response_data = json_decode($response_body, true);
		
		if ($response_code !== 200 || !is_array($response_data)) {
			return array('error' => __('Failed to check transaction status.', 'payop-woocommerce'));
		}
		
		return $response_data;
	}

	/**
	 * Filter payment methods by supported currency
	 *
	 * @param array $payment_methods Array of payment methods
	 * @param string $currency Currency code (e.g., 'USD', 'EUR')
	 * @return array Filtered payment methods
	 */
	public function filter_payment_methods_by_currency($payment_methods, $currency) {
		$filtered_methods = array();
		$currency = strtoupper($currency);
		
		foreach ($payment_methods as $method) {
			$supported_currencies = isset($method['currencies']) ? $method['currencies'] : array();
			$supported_currencies = array_map('strtoupper', $supported_currencies);
			
			if (in_array($currency, $supported_currencies)) {
				$filtered_methods[] = $method;
			} else {
				if (function_exists('payop_debug')) {
					$method_name = isset($method['title']) ? $method['title'] : $method['identifier'];
					payop_debug("Payment method '$method_name' (ID: {$method['identifier']}) does not support currency '$currency'. Supported: " . implode(', ', $supported_currencies), 'info');
				}
			}
		}
		
		if (function_exists('payop_debug')) {
			payop_debug('Filtered ' . count($filtered_methods) . ' payment methods for currency ' . $currency . ' out of ' . count($payment_methods) . ' total', 'info');
		}
		
		return $filtered_methods;
	}
}