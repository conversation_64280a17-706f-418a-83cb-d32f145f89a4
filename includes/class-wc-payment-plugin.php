<?php
/**
 * WooCommerce Payop Payment Gateway Plugin.
 *
 * @extends WC_Payment_Gateway
 * @version 1.1.0
 */

if (!defined('ABSPATH')) {
	exit;
}

class Payop_WC_Payment_Plugin {

	public function __construct() {
		add_action('plugins_loaded', [$this, 'on_plugins_loaded'], 0);
		add_action('before_woocommerce_init', [$this, 'declare_compatibility']);
		
		// Load payment methods handler
		add_action('init', [$this, 'init_payment_methods_handler']);
		
		// Enqueue front-end scripts and styles
		add_action('wp_enqueue_scripts', [$this, 'enqueue_frontend_assets']);
		
		// Register hooks for dynamic payment methods
		add_action('wp_loaded', [$this, 'register_payment_methods_hooks']);
	}

	/**
	 * Initialize payment methods handler
	 */
	public function init_payment_methods_handler() {
		require_once PAYOP_PLUGIN_PATH . '/includes/class-wc-payop-payment-methods.php';
		$payment_methods_handler = new WC_Payop_Payment_Methods();
	}

	/**
	 * Enqueue frontend scripts and styles
	 */
	public function enqueue_frontend_assets() {
		if (is_checkout()) {
			wp_enqueue_style(
				'payop-payment-methods-checkout',
				PAYOP_PLUGIN_URL . 'css/payment-methods-checkout.css',
				array(),
				filemtime(PAYOP_PLUGIN_PATH . 'css/payment-methods-checkout.css')
			);
			
			wp_enqueue_script(
				'payop-payment-methods-checkout',
				PAYOP_PLUGIN_URL . 'js/payment-methods-checkout.js',
				array('jquery'),
				filemtime(PAYOP_PLUGIN_PATH . 'js/payment-methods-checkout.js'),
				true
			);
		}
	}
	
	/**
	 * Actions to perform after all plugins are loaded.
	 */
	public function on_plugins_loaded() {
		$environment_check = $this->is_supported_environment();

		if (!$environment_check['result']) {
			$this->display_admin_notice($environment_check['message']);
			return;
		}

		$this->load_gateway();
		add_filter('plugin_action_links_' . PAYOP_PLUGIN_BASENAME, [$this, 'add_settings_link']);
		add_filter('woocommerce_payment_gateways', [$this, 'add_payop_gateway']);
		add_action('woocommerce_blocks_loaded', [$this, 'register_payment_method_type']);
		
		// Add admin scripts and styles
		add_action('admin_enqueue_scripts', [$this, 'enqueue_admin_assets']);
	}

	/**
	 * Enqueue admin scripts and styles
	 * 
	 * @param string $hook Current admin page hook
	 */
	public function enqueue_admin_assets($hook) {
		// Only load on WooCommerce payment settings page
		if (strpos($hook, 'wc-settings') !== false && isset($_GET['section']) && $_GET['section'] === PAYOP_PAYMENT_GATEWAY_NAME) {
			wp_enqueue_style(
				'payop-payment-methods-admin',
				PAYOP_PLUGIN_URL . 'css/payment-methods-admin.css',
				array(),
				filemtime(PAYOP_PLUGIN_PATH . 'css/payment-methods-admin.css')
			);
			
			wp_enqueue_script(
				'payop-payment-methods-admin',
				PAYOP_PLUGIN_URL . 'js/payment-methods-admin.js',
				array('jquery', 'jquery-ui-sortable'),
				filemtime(PAYOP_PLUGIN_PATH . 'js/payment-methods-admin.js'),
				true
			);
			
			wp_localize_script('payop-payment-methods-admin', 'payop_admin_i18n', array(
				'ajax_url' => admin_url('admin-ajax.php'),
				'nonce' => wp_create_nonce('payop_refresh_payment_methods'),
				'missing_credentials' => __('Please enter your Public Key and Project ID first.', 'payop-woocommerce'),
				'refresh_success' => __('Payment methods refreshed successfully.', 'payop-woocommerce'),
				'save_success' => __('Payment methods saved successfully.', 'payop-woocommerce'),
				'ajax_error' => __('Failed to communicate with server.', 'payop-woocommerce'),
				'generic_error' => __('An error occurred.', 'payop-woocommerce'),
				'no_methods' => __('No payment methods available.', 'payop-woocommerce'),
			));
		}
	}

	/**
	 * Check if the environment supports the plugin.
	 *
	 * @return array Result of the environment check containing 'result' (bool) and 'message' (string).
	 */
	private function is_supported_environment() {
		$result = true;
		$message = '';

		// Check minimum PHP version.
		if (version_compare(PHP_VERSION, PAYOP_MIN_PHP_VERSION, '<')) {
			$result = false;
			$message = __('The "' . PAYOP_PLUGIN_NAME . '" plugin requires PHP ' . PAYOP_MIN_PHP_VERSION . ' or later. Please upgrade your PHP version.', 'payop-woocommerce');
		}

		// Check minimum Wordpress version.
		if (version_compare(get_bloginfo('version'), PAYOP_MIN_WP_VERSION, '<')) {
			$result = false;
			$message = __('The "' . PAYOP_PLUGIN_NAME . '" plugin requires Wordpress ' . PAYOP_MIN_WP_VERSION . ' or later. Please upgrade your Wordpress version.', 'payop-woocommerce');
		}

		// Check if WooCommerce is active.
		if (!$this->is_wc_active()) {
			$result = false;
			$message = __('For the correct operation of the "' . PAYOP_PLUGIN_NAME . '" plugin, WooCommerce is required. Please install and activate WooCommerce.', 'payop-woocommerce');
		}

		// Check minimum WooCommerce version.
		if ($this->is_wc_active() && version_compare(WC()->version, PAYOP_MIN_WC_VERSION, '<')) {
			$result = false;
			$message = __('The "' . PAYOP_PLUGIN_NAME . '" plugin requires WooCommerce ' . PAYOP_MIN_WC_VERSION . ' or later. Please upgrade your WooCommerce version.', 'payop-woocommerce');
		}

		return ['result' => $result, 'message' => $message];
	}

	/**
	 * Check if WooCommerce is active.
	 *
	 * @return bool Whether WooCommerce is active.
	 */
	private function is_wc_active() {
		return in_array( 'woocommerce/woocommerce.php', apply_filters( 'active_plugins', get_option( 'active_plugins' ) ) ) && class_exists('WC_Payment_Gateway');
	}

	/**
	 * Load Payop payment gateway class.
	 * 
	 * @uses \WC_Payment_Gateway
	 */
	private function load_gateway() {
		require_once PAYOP_PLUGIN_PATH . '/includes/class-wc-gateway-payop.php';
	}

	/**
	 * Declare compatibility with cart_checkout_blocks feature.
	 * 
	 * @uses \Automattic\WooCommerce\Utilities\FeaturesUtil
	 */
	public function declare_compatibility() {
		if (class_exists('\Automattic\WooCommerce\Utilities\FeaturesUtil')) {
			\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('cart_checkout_blocks', PAYOP_PLUGIN_FILE, true);
			\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility('custom_order_tables', PAYOP_PLUGIN_FILE, true);
		}
	}

	/**
	 * Register Payop payment method type for blocks.
	 * 
	 * @uses \Automattic\WooCommerce\Blocks\Payments\PaymentMethodRegistry
	 */
	public function register_payment_method_type() {
		if (class_exists('\Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType')) {
			require_once PAYOP_PLUGIN_PATH . '/includes/class-wc-gateway-payop-blocks.php';

			// Register the Payop Blocks class with WooCommerce payment method registry.
			add_action(
				'woocommerce_blocks_payment_method_type_registration',
				function (\Automattic\WooCommerce\Blocks\Payments\PaymentMethodRegistry $payment_method_registry) {
					$payment_method_registry->register(new WC_Gateway_Payop_Blocks());
				}
			);
		}
	}

	/**
	 * Add settings link to the plugin actions.
	 *
	 * @param array $links Existing plugin action links.
	 * @return array Modified plugin action links.
	 */
	public function add_settings_link($links) {
		$url = admin_url('admin.php?page=wc-settings&tab=checkout&section='.PAYOP_PAYMENT_GATEWAY_NAME);
		$links[] = '<a href="' . esc_url($url) . '">' . __('Settings', 'payop-woocommerce') . '</a>';
		return $links;
	}

	/**
	 * Add the Payop gateway to WooCommerce.
	 *
	 * @param array $gateways Existing payment gateways.
	 * @return array Modified payment gateways.
	 */
	public function add_payop_gateway($gateways) {
		// Always add the main gateway first
		$gateways[] = 'WC_Gateway_Payop';

		// Add individual payment method gateways if not in admin
		if (!is_admin() && class_exists('WC_Gateway_Payop')) {
			// Load required helper classes
			if (!class_exists('WC_Payop_Payment_Method_Helper')) {
				require_once PAYOP_PLUGIN_PATH . '/includes/class-wc-payop-payment-method-helper.php';
			}
			$main_gateway = new WC_Gateway_Payop();
			$selected_methods = $main_gateway->get_selected_payment_methods();

			if (function_exists('payop_debug')) {
				payop_debug('Gateway registration: Found ' . count($selected_methods) . ' selected payment methods', 'info');
			}

			if (!empty($selected_methods)) {
				if (function_exists('payop_debug')) {
					payop_debug('Adding payment method gateways to WooCommerce: ' . count($selected_methods) . ' methods', 'info');
				}

				// Get available payment methods
				$available_methods = $main_gateway->payment_methods_handler->get_available_payment_methods(
					$main_gateway->jwt_token,
					$main_gateway->project_id
				);

				if (function_exists('payop_debug')) {
					if (isset($available_methods['error'])) {
						payop_debug('Gateway registration: API error - ' . $available_methods['error'], 'error');
					} else {
						payop_debug('Gateway registration: Got ' . (is_array($available_methods) ? count($available_methods) : 0) . ' available methods from API', 'info');
					}
				}

				if (is_array($available_methods) && !isset($available_methods['error'])) {
					// Get store currency
					$store_currency = get_woocommerce_currency();

					// Filter by currency (but include all for now)
					$currency_filtered_methods = $main_gateway->payment_methods_handler->filter_payment_methods_by_currency($available_methods, $store_currency);

					// Get selected methods in order
					$selected_method_objects = array();
					foreach ($selected_methods as $method_id) {
						$normalized_method_id = WC_Payop_Payment_Method_Helper::normalize_method_id($method_id);

						// First try currency-filtered methods
						$found_method = null;
						foreach ($currency_filtered_methods as $method) {
							$identifier = WC_Payop_Payment_Method_Helper::get_method_identifier($method);
							if ($identifier == $normalized_method_id) {
								$found_method = $method;
								break;
							}
						}

						// Fallback to all methods
						if (!$found_method) {
							foreach ($available_methods as $method) {
								$identifier = WC_Payop_Payment_Method_Helper::get_method_identifier($method);
								if ($identifier == $normalized_method_id) {
									$found_method = $method;
									break;
								}
							}
						}

						if ($found_method) {
							$selected_method_objects[] = $found_method;
						}
					}

					// Store payment method configurations globally
					if (!isset($GLOBALS['payop_payment_method_configs'])) {
						$GLOBALS['payop_payment_method_configs'] = array();
					}

					// Create gateway classes for each selected method
					foreach ($selected_method_objects as $method) {
						$method_id = WC_Payop_Payment_Method_Helper::get_method_identifier($method);
						$gateway_id = WC_Payop_Payment_Method_Helper::generate_gateway_id($method_id);

						// Store the configuration for this payment method
						$GLOBALS['payop_payment_method_configs'][$method_id] = array(
							'id' => $gateway_id,
							'payment_method_id' => $method_id,
							'title' => $method['title'],
							'icon' => ($main_gateway->show_payment_logos === 'yes' ? $method['logo'] : ''),
							'has_fields' => true,
							'is_payment_method_instance' => true,
							'method_title' => sprintf(__('Payop - %s', 'payop-woocommerce'), $method['title']),
							'enabled' => $main_gateway->enabled,
							'description' => $main_gateway->description,
							'public_key' => $main_gateway->public_key,
							'secret_key' => $main_gateway->secret_key,
							'project_id' => $main_gateway->project_id,
							'jwt_token' => $main_gateway->jwt_token,
							'skip_confirm' => $main_gateway->skip_confirm,
							'direct_redirect' => $main_gateway->direct_redirect,
							'language' => $main_gateway->language,
							'instructions' => $main_gateway->instructions,
							'payment_method_data' => $method,
						);

						// Create a dynamic class for this payment method
						$class_name = 'WC_Gateway_Payop_Method_' . $method_id;

						if (!class_exists($class_name)) {
							// Use a safer approach to create the class
							$this->create_payment_method_class($class_name, $method_id);
						}

						$gateways[] = $class_name;

						if (function_exists('payop_debug')) {
							payop_debug("Added payment method gateway: {$method['title']} (Class: $class_name, ID: $gateway_id)", 'info');
						}
					}
				}
			}
		}

		return $gateways;
	}

	/**
	 * Create a payment method class dynamically
	 *
	 * @param string $class_name The class name to create
	 * @param int $method_id The payment method ID
	 */
	private function create_payment_method_class($class_name, $method_id) {
		// Create the class using eval with proper error handling
		$class_code = "
			class $class_name extends WC_Gateway_Payop {
				public function __construct() {
					if (isset(\$GLOBALS['payop_payment_method_configs'][$method_id])) {
						\$config = \$GLOBALS['payop_payment_method_configs'][$method_id];
						parent::__construct(\$config);
					} else {
						parent::__construct();
					}
				}
			}
		";

		try {
			eval($class_code);
		} catch (ParseError $e) {
			if (function_exists('payop_debug')) {
				payop_debug("Failed to create payment method class $class_name: " . $e->getMessage(), 'error');
			}
		}
	}

	/**
	 * Display an admin notice.
	 *
	 * @param string $message The message to display in the admin notice.
	 */
	private function display_admin_notice($message) {
		add_action('admin_notices', function() use ($message) {
			echo '<div class="notice notice-error"><p>';
			echo esc_html($message);
			echo '</p></div>';
		});
	}

	/**
	 * Register hooks for dynamic payment methods
	 *
	 * This ensures that our cloned gateways with IDs like 'payop_123' work correctly
	 * with WooCommerce's payment processing system
	 */
	public function register_payment_methods_hooks() {
		// Add receipt page action for all Payop payment methods
		add_action('woocommerce_receipt_payop', [$this, 'receipt_page_handler'], 10, 1);

		// Add thank you page action for all Payop payment methods
		add_action('woocommerce_thankyou_payop', [$this, 'thankyou_page_handler'], 10, 1);

		// Register dynamic hooks for all Payop payment methods
		if (class_exists('WC_Gateway_Payop')) {
			$main_gateway = new WC_Gateway_Payop();

			// Get all selected payment methods
			$selected_methods = $main_gateway->get_selected_payment_methods();

			if (!empty($selected_methods)) {
				foreach ($selected_methods as $method_id) {
					$gateway_id = PAYOP_PAYMENT_GATEWAY_NAME . '_' . $method_id;

					// Register receipt page action for this specific method
					add_action('woocommerce_receipt_' . $gateway_id, [$this, 'receipt_page_handler'], 10, 1);

					// Register thank you page action for this specific method
					add_action('woocommerce_thankyou_' . $gateway_id, [$this, 'thankyou_page_handler'], 10, 1);
				}
			}


		}
	}
	
	/**
	 * Handler for receipt page for all Payop payment methods
	 * 
	 * @param int $order_id Order ID
	 */
	public function receipt_page_handler($order_id) {
		$main_gateway = new WC_Gateway_Payop();
		$main_gateway->receipt_page($order_id);
	}
	
	/**
	 * Handler for thank you page for all Payop payment methods
	 * 
	 * @param int $order_id Order ID
	 */
	public function thankyou_page_handler($order_id) {
		$main_gateway = new WC_Gateway_Payop();
		$main_gateway->receipt_page($order_id);
	}
}
