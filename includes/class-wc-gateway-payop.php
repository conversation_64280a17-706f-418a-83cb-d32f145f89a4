<?php
/**
 * WooCommerce Payop Payment Gateway.
 *
 * @extends WC_Payment_Gateway
 * @version 1.1.0
 */

if (!defined('ABSPATH')) {
	exit;
}

class WC_Gateway_Payop extends WC_Payment_Gateway {

	/**
	 * Public key for authentication with Payop API.
	 *
	 * @var string
	 */
	public $public_key;

	/**
	 * URL for making requests to Payop API.
	 *
	 * @var string
	 */
	public $api_url;

	/**
	 * Secret key for signing requests to Payop API.
	 *
	 * @var string
	 */
	public $secret_key;

	/**
	 * Project ID for Payop API.
	 *
	 * @var string
	 */
	public $project_id;

	/**
	 * JWT token for Payop API authentication.
	 *
	 * @var string
	 */
	public $jwt_token;

	/**
	 * Flag indicating whether to skip confirmation step before payment.
	 *
	 * @var string
	 */
	public $skip_confirm;

	/**
	 * Flag indicating whether to direct redirect to payment method page.
	 *
	 * @var string
	 */
	public $direct_redirect;

	/**
	 * Lifetime of the payment link.
	 *
	 * @var string
	 */
	public $lifetime;

	/**
	 * Flag indicating whether orders should be auto-completed after successful payment.
	 *
	 * @var string
	 */
	public $auto_complete;

	/**
	 * Language code for the payment form.
	 *
	 * @var string
	 */
	public $language;

	/**
	 * Instructions for the payment.
	 *
	 * @var string
	 */
	public $instructions;

	/**
	 * Selected payment methods.
	 *
	 * @var array
	 */
	public $selected_payment_methods;

	/**
	 * Flag indicating whether to show payment logos.
	 *
	 * @var string
	 */
	public $show_payment_logos;

	/**
	 * ID of the specific payment method, if this is a cloned gateway.
	 *
	 * @var string
	 */
	public $payment_method_id;

	/**
	 * Payment methods handler instance.
	 *
	 * @var WC_Payop_Payment_Methods
	 */
	protected $payment_methods_handler;

	/**
	 * Flag indicating if this is a specific payment method instance
	 * 
	 * @var bool
	 */
	public $is_payment_method_instance = false;

	/**
	 * Encryption key for sensitive data
	 *
	 * @var string
	 */
	private $encryption_key;

	/**
	 * Constructor
	 *
	 * @param array $args Optional arguments to customize the gateway
	 */
	public function __construct($args = array())
	{
		$this->api_url = 'https://api.payop.com/v1/invoices/create';
		$this->id = PAYOP_PAYMENT_GATEWAY_NAME;
		$this->icon = apply_filters('woocommerce_payop_icon', PAYOP_PLUGIN_URL . '/payop.png');
		$this->has_fields = false; // Will be set to true for payment method instances
		$this->method_title = __('Payop', 'payop-woocommerce');
		$this->method_description = __('Take payments via Payop payment gateway.', 'payop-woocommerce');
		$this->supports = array('products');

		// Load the payment methods handler
		if (!class_exists('WC_Payop_Payment_Methods')) {
			require_once PAYOP_PLUGIN_PATH . '/includes/class-wc-payop-payment-methods.php';
		}
		
		$this->payment_methods_handler = new WC_Payop_Payment_Methods();

		// Apply any custom args (for payment method specific instances)
		if (!empty($args)) {
			foreach ($args as $key => $value) {
				$this->$key = $value;
			}
		}

		// Only do init tasks if this is the main gateway or we have a payment method ID
		if (!$this->is_payment_method_instance) {
			// Load the settings
			$this->init_form_fields();
			$this->init_settings();

			// Define user set variables
			$this->title = $this->get_option('title');
			$this->public_key = $this->get_option('public_key');
			$this->secret_key = $this->get_secure_option('secret_key');
			$this->project_id = $this->get_option('project_id');
			$this->jwt_token = $this->get_secure_option('jwt_token');
			$this->skip_confirm = $this->get_option('skip_confirm');
			$this->direct_redirect = $this->get_option('direct_redirect');
			$this->lifetime = $this->get_option('lifetime');
			$this->auto_complete = $this->get_option('auto_complete');
			$this->language = 'en';
			$this->description = $this->get_option('description');
			$this->instructions = $this->get_option('instructions');
			$this->selected_payment_methods = $this->get_selected_payment_methods();
			$this->show_payment_logos = $this->get_option('show_payment_logos', 'yes');

			// Actions for the main gateway
			add_action('payop-ipn-request', [$this, 'successful_request']);
			add_action('woocommerce_receipt_' . $this->id, [$this, 'receipt_page']);
			add_action('woocommerce_thankyou_' . $this->id, [$this, 'receipt_page']);
			add_filter('woocommerce_order_needs_payment', [$this, 'prevent_payment_for_failed_orders'], 10, 3);
			add_action('woocommerce_my_account_my_orders_actions', [$this, 'hide_pay_button_for_failed_orders'], 10, 2);
			add_filter('render_block', [$this, 'modify_wc_order_confirmation_block_content'], 10, 2);
			add_action('woocommerce_api_wc_' . $this->id, [$this, 'check_ipn_response']);
			add_action('woocommerce_update_options_payment_gateways_' . $this->id, [$this, 'process_admin_options']);
			add_action('woocommerce_settings_api_form_fields_' . $this->id, array($this, 'add_custom_form_fields'));
			add_action('admin_enqueue_scripts', array($this, 'admin_enqueue_scripts'));
			add_action('woocommerce_checkout_update_order_meta', array($this, 'add_payment_method_to_order'));
			add_filter('woocommerce_available_payment_gateways', array($this, 'filter_available_payment_gateways'));

			// Payment method registration is now handled in the main plugin class

			// Add hooks for frontend checkout
			add_action('wp_enqueue_scripts', array($this, 'enqueue_checkout_assets'));
			add_action('wp_ajax_payop_get_payment_method_fields', array($this, 'ajax_get_payment_method_fields'));
			add_action('wp_ajax_nopriv_payop_get_payment_method_fields', array($this, 'ajax_get_payment_method_fields'));

			if (!$this->is_valid_for_use()) {
				$this->enabled = false;
			}
		}
	}

	/**
	 * Get encryption key for sensitive data
	 *
	 * @return string Encryption key
	 */
	private function get_encryption_key() {
		if (empty($this->encryption_key)) {
			// Use WordPress salts for encryption key
			$this->encryption_key = hash('sha256', SECURE_AUTH_KEY . SECURE_AUTH_SALT . $this->id);
		}
		return $this->encryption_key;
	}

	/**
	 * Encrypt sensitive data
	 *
	 * @param string $data Data to encrypt
	 * @return string Encrypted data
	 */
	private function encrypt_data($data) {
		if (empty($data)) {
			return '';
		}

		$key = $this->get_encryption_key();
		$iv = openssl_random_pseudo_bytes(16);
		$encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);

		return base64_encode($iv . $encrypted);
	}

	/**
	 * Decrypt sensitive data
	 *
	 * @param string $encrypted_data Encrypted data
	 * @return string Decrypted data
	 */
	private function decrypt_data($encrypted_data) {
		if (empty($encrypted_data)) {
			return '';
		}

		$key = $this->get_encryption_key();
		$data = base64_decode($encrypted_data);

		if (strlen($data) < 16) {
			return $encrypted_data; // Return as-is if not encrypted (backward compatibility)
		}

		$iv = substr($data, 0, 16);
		$encrypted = substr($data, 16);

		$decrypted = openssl_decrypt($encrypted, 'AES-256-CBC', $key, 0, $iv);

		return $decrypted !== false ? $decrypted : $encrypted_data;
	}

	/**
	 * Get secure setting value (decrypt if needed)
	 *
	 * @param string $key Setting key
	 * @param string $default Default value
	 * @return string Setting value
	 */
	private function get_secure_option($key, $default = '') {
		$value = $this->get_option($key, $default);

		// Decrypt sensitive fields
		if (in_array($key, array('secret_key', 'jwt_token')) && !empty($value)) {
			return $this->decrypt_data($value);
		}

		return $value;
	}

	/**
	 * Sanitize payment field value based on field type
	 *
	 * @param string $field_name Field name
	 * @param mixed $value Field value
	 * @return string Sanitized value
	 */
	private function sanitize_payment_field($field_name, $value) {
		// Basic sanitization for all fields
		$value = trim($value);

		// Field-specific sanitization
		switch (strtolower($field_name)) {
			case 'email':
				return sanitize_email($value);

			case 'phone':
			case 'mobile':
			case 'telephone':
				// Remove all non-numeric characters except + and spaces
				return preg_replace('/[^0-9+\s-]/', '', $value);

			case 'document':
			case 'cpf':
			case 'cnpj':
			case 'rut':
			case 'dni':
				// Remove all non-alphanumeric characters
				return preg_replace('/[^a-zA-Z0-9]/', '', $value);

			case 'bank_code':
			case 'account_number':
				// Only numbers allowed
				return preg_replace('/[^0-9]/', '', $value);

			case 'amount':
				// Ensure proper decimal format
				return number_format(floatval($value), 2, '.', '');

			default:
				// Default text sanitization
				return sanitize_text_field($value);
		}
	}

	/**
	 * Validate payment field value
	 *
	 * @param string $field_name Field name
	 * @param string $value Field value
	 * @param int $payment_method_id Payment method ID
	 * @return array Validation result with 'valid' boolean and 'error' string
	 */
	private function validate_payment_field($field_name, $value, $payment_method_id) {
		// Get field configuration from payment method
		$field_config = $this->get_field_config($field_name, $payment_method_id);

		if (!$field_config) {
			return array('valid' => true, 'error' => ''); // Unknown field, skip validation
		}

		// Use helper class for validation
		return WC_Payop_Payment_Method_Helper::validate_field_value($value, $field_config);
	}

	/**
	 * Get field configuration for a specific payment method
	 *
	 * @param string $field_name Field name
	 * @param int $payment_method_id Payment method ID
	 * @return array|null Field configuration or null if not found
	 */
	private function get_field_config($field_name, $payment_method_id) {
		// Get payment method details
		$available_methods = $this->payment_methods_handler->get_available_payment_methods(
			$this->jwt_token,
			$this->project_id
		);

		if (isset($available_methods['error'])) {
			return null;
		}

		// Find the specific payment method
		foreach ($available_methods as $method) {
			$method_id = WC_Payop_Payment_Method_Helper::get_method_identifier($method);
			if ($method_id == $payment_method_id) {
				$required_fields = WC_Payop_Payment_Method_Helper::get_required_fields($method);

				// Find the specific field
				foreach ($required_fields as $field) {
					if (isset($field['name']) && $field['name'] === $field_name) {
						return $field;
					}
				}
				break;
			}
		}

		return null;
	}

	/**
	 * Process admin options and encrypt sensitive data
	 *
	 * @return bool
	 */
	public function process_admin_options() {
		// Get the posted data
		$post_data = $this->get_post_data();

		// Encrypt sensitive fields before saving
		if (isset($post_data['woocommerce_' . $this->id . '_secret_key'])) {
			$secret_key = $post_data['woocommerce_' . $this->id . '_secret_key'];
			if (!empty($secret_key)) {
				$post_data['woocommerce_' . $this->id . '_secret_key'] = $this->encrypt_data($secret_key);
			}
		}

		if (isset($post_data['woocommerce_' . $this->id . '_jwt_token'])) {
			$jwt_token = $post_data['woocommerce_' . $this->id . '_jwt_token'];
			if (!empty($jwt_token)) {
				$post_data['woocommerce_' . $this->id . '_jwt_token'] = $this->encrypt_data($jwt_token);
			}
		}

		// Set the modified post data
		$_POST = array_merge($_POST, $post_data);

		// Call parent method to save
		$result = parent::process_admin_options();

		// Log credential update for security audit
		if (function_exists('payop_debug')) {
			payop_debug('PayOp credentials updated by user: ' . get_current_user_id(), 'info');
		}

		return $result;
	}

	/**
	 * Register individual payment methods as separate gateways
	 */
	public function register_payment_method_gateways() {
		// Skip if in admin area or if no selected methods
		if (is_admin()) {
			if (function_exists('payop_debug')) {
				payop_debug('Skipping payment method registration - in admin area', 'info');
			}
			return;
		}

		if (empty($this->selected_payment_methods)) {
			if (function_exists('payop_debug')) {
				payop_debug('Skipping payment method registration - no selected methods', 'info');
			}
			return;
		}

		if (function_exists('payop_debug')) {
			payop_debug('Starting payment method registration with ' . count($this->selected_payment_methods) . ' selected methods', 'info');
		}

		// Get current store currency
		$store_currency = get_woocommerce_currency();

		// Get all available payment methods
		$available_methods = $this->payment_methods_handler->get_available_payment_methods(
			$this->jwt_token,
			$this->project_id
		);

		// Skip if no methods found
		if (!is_array($available_methods) || isset($available_methods['error'])) {
			if (function_exists('payop_debug')) {
				$error = isset($available_methods['error']) ? $available_methods['error'] : 'Unknown error';
				payop_debug('Failed to get available payment methods: ' . $error, 'error');
			}
			return;
		}

		if (function_exists('payop_debug')) {
			payop_debug('Total available payment methods: ' . count($available_methods), 'info');
			payop_debug('Selected payment method IDs: ' . implode(', ', $this->selected_payment_methods), 'info');
		}

		// Filter payment methods by current store currency
		$currency_filtered_methods = $this->payment_methods_handler->filter_payment_methods_by_currency($available_methods, $store_currency);

		if (function_exists('payop_debug')) {
			payop_debug('Payment methods after currency filter (' . $store_currency . '): ' . count($currency_filtered_methods), 'info');
		}

		// Get only selected payment methods in the correct order
		$selected_methods = array();
		foreach ($this->selected_payment_methods as $method_id) {
			$normalized_method_id = WC_Payop_Payment_Method_Helper::normalize_method_id($method_id);

			// First try to find in currency-filtered methods
			$found_method = null;
			foreach ($currency_filtered_methods as $method) {
				$identifier = WC_Payop_Payment_Method_Helper::get_method_identifier($method);
				if ($identifier == $normalized_method_id) {
					$found_method = $method;
					break;
				}
			}

			// If not found in currency-filtered, try all methods and include them anyway for now
			if (!$found_method) {
				foreach ($available_methods as $method) {
					$identifier = WC_Payop_Payment_Method_Helper::get_method_identifier($method);
					if ($identifier == $normalized_method_id) {
						$found_method = $method;
						if (function_exists('payop_debug')) {
							$currencies = isset($method['currencies']) ? implode(', ', $method['currencies']) : 'none';
							payop_debug("Payment method {$method['title']} (ID: $normalized_method_id) does not support store currency $store_currency. Supported: $currencies. Including anyway for testing.", 'warning');
						}
						break;
					}
				}
			}

			if ($found_method) {
				$selected_methods[] = $found_method;
			} else {
				if (function_exists('payop_debug')) {
					payop_debug("Selected payment method ID $normalized_method_id not found in available methods", 'warning');
				}
			}
		}

		if (function_exists('payop_debug')) {
			payop_debug('Final selected methods to register: ' . count($selected_methods), 'info');
		}

		// Register each selected method as a separate gateway
		foreach ($selected_methods as $method) {
			$method_id = WC_Payop_Payment_Method_Helper::get_method_identifier($method);
			$gateway_id = WC_Payop_Payment_Method_Helper::generate_gateway_id($method_id);

			if (function_exists('payop_debug')) {
				payop_debug("Registering payment method: {$method['title']} (ID: $method_id, Gateway ID: $gateway_id)", 'info');
			}

			// Register the method gateway with WooCommerce
			add_filter('woocommerce_payment_gateways', function($gateways) use ($method, $method_id, $gateway_id) {
				// Create a new instance for this payment method
				$method_gateway = new WC_Gateway_Payop(array(
					'id' => $gateway_id,
					'payment_method_id' => $method_id,
					'title' => $method['title'],
					'icon' => $this->show_payment_logos === 'yes' ? $method['logo'] : '',
					'has_fields' => true, // Enable fields for payment method instances
					'is_payment_method_instance' => true,
					'method_title' => sprintf(__('Payop - %s', 'payop-woocommerce'), $method['title']),
					'enabled' => $this->enabled,
					'description' => $this->description,
					'public_key' => $this->public_key,
					'secret_key' => $this->secret_key,
					'project_id' => $this->project_id,
					'jwt_token' => $this->jwt_token,
					'skip_confirm' => $this->skip_confirm,
					'direct_redirect' => $this->direct_redirect,
					'language' => $this->language,
					'instructions' => $this->instructions,
					'payment_method_data' => $method, // Store the full method data
				));

				$gateways[] = $method_gateway;
				return $gateways;
			});

			// Register receipt and thank you page handlers for this payment method
			add_action('woocommerce_receipt_' . $gateway_id, array($this, 'receipt_page'));
			add_action('woocommerce_thankyou_' . $gateway_id, array($this, 'receipt_page'));
		}
	}

	/**
	 * Process the payment and return the result.
	 *
	 * @param int $order_id Order ID.
	 * @return array
	 */
	public function process_payment($order_id) {
		// Log the start of payment processing for debugging
		if (function_exists('payop_debug')) {
			payop_debug("Processing payment for order #$order_id with gateway ID: {$this->id}", 'info');
		}
		
		$order = wc_get_order($order_id);
		
		// Get the payment method ID from either the gateway instance or POST data
		$payment_method_id = '';
		
		// Check if this is a specific payment method gateway
		if (isset($this->payment_method_id) && !empty($this->payment_method_id)) {
			$payment_method_id = WC_Payop_Payment_Method_Helper::normalize_method_id($this->payment_method_id);
			if (function_exists('payop_debug')) {
				payop_debug("Using payment method from gateway instance: $payment_method_id", 'info');
			}
		}
		// Check for explicit payop_payment_method hidden field
		else if (isset($_POST['payop_payment_method'])) {
			$payment_method_id = WC_Payop_Payment_Method_Helper::normalize_method_id(sanitize_text_field($_POST['payop_payment_method']));
			if (function_exists('payop_debug')) {
				payop_debug("Using payment method from hidden field: $payment_method_id", 'info');
			}
		}
		// Also check if payment_method was passed via POST contains a method ID (fallback)
		else if (isset($_POST['payment_method']) && strpos($_POST['payment_method'], PAYOP_PAYMENT_GATEWAY_NAME . '_') === 0) {
			// Extract payment method ID from the gateway ID (e.g., payop_123 -> 123)
			$payment_method_id = WC_Payop_Payment_Method_Helper::extract_method_id_from_gateway_id(sanitize_text_field($_POST['payment_method']));
			if (function_exists('payop_debug')) {
				payop_debug("Extracted payment method ID from POST: $payment_method_id", 'info');
			}
		}
		
		// Save the payment method to order meta if we found one
		if (!empty($payment_method_id)) {
			$order->update_meta_data('_payop_payment_method_id', $payment_method_id);
			
			// Save payment method fields if they were submitted
			$this->save_payment_method_fields($order, $payment_method_id);
			
			$order->save_meta_data();
		} else {
			if (function_exists('payop_debug')) {
				payop_debug("No payment method ID found for order #$order_id", 'warning');
			}
		}
		
		// Try direct integration flow if we have a payment method
		if (!empty($payment_method_id) && $this->should_use_direct_integration()) {
			if (function_exists('payop_debug')) {
				payop_debug("Attempting direct integration for payment method ID: $payment_method_id", 'info');
			}

			$redirect_result = $this->process_direct_integration($order, $payment_method_id);
			if ($redirect_result) {
				if (function_exists('payop_debug')) {
					payop_debug("Direct integration successful, redirecting to: " . $redirect_result['redirect'], 'info');
				}
				return $redirect_result;
			} else {
				if (function_exists('payop_debug')) {
					payop_debug("Direct integration failed, falling back to hosted checkout", 'warning');
				}
			}
		}

		// Fallback to hosted checkout if direct integration is not available or failed
		if (!empty($payment_method_id)) {
			if (function_exists('payop_debug')) {
				payop_debug("Using hosted checkout for payment method ID: $payment_method_id", 'info');
			}

			// Empty the cart before redirecting
			$this->empty_cart();

			// Return redirect to receipt page where hosted checkout will be handled
			return array(
				'result'   => 'success',
				'redirect' => $order->get_checkout_payment_url(true),
			);
		}

		// If no payment method ID found, log error and redirect to thank you page
		if (function_exists('payop_debug')) {
			payop_debug("No payment method ID found, redirecting to thank you page", 'error');
		}

		// Empty the cart
		$this->empty_cart();

		// Return thankyou redirect
		return array(
			'result'   => 'success',
			'redirect' => $this->get_return_url($order),
		);
	}

	/**
	 * Filter available payment gateways to hide main gateway when payment method gateways exist
	 *
	 * @param array $gateways Available payment gateways.
	 * @return array Modified gateways.
	 */
	public function filter_available_payment_gateways($gateways) {
		// Hide the main payop gateway if we have payment method gateways
		if ($this->id === PAYOP_PAYMENT_GATEWAY_NAME && !empty($this->selected_payment_methods)) {
			// Count how many payop_* gateways are available
			$payop_method_gateways = 0;
			foreach ($gateways as $id => $gateway) {
				// Check for both gateway IDs and gateway objects
				$gateway_id = is_object($gateway) ? $gateway->id : $id;
				if (strpos($gateway_id, PAYOP_PAYMENT_GATEWAY_NAME . '_') === 0) {
					$payop_method_gateways++;
				}
			}

			if (function_exists('payop_debug')) {
				payop_debug("Found $payop_method_gateways PayOp payment method gateways", 'info');
			}

			// Only hide the main gateway if we have payment method gateways
			if ($payop_method_gateways > 0 && isset($gateways[PAYOP_PAYMENT_GATEWAY_NAME])) {
				unset($gateways[PAYOP_PAYMENT_GATEWAY_NAME]);
				if (function_exists('payop_debug')) {
					payop_debug("Hidden main PayOp gateway since payment method gateways are available", 'info');
				}
			}
		}

		return $gateways;
	}

	/**
	 * Enqueue scripts and styles for admin.
	 */
	public function admin_enqueue_scripts() {
		$screen = get_current_screen();

		// Only load on WooCommerce payment settings page
		if ($screen && strpos($screen->id, 'wc-settings-checkout') !== false) {
			wp_enqueue_script('jquery-ui-sortable');

			wp_enqueue_script(
				'payop-payment-methods-admin',
				PAYOP_PLUGIN_URL . 'js/payment-methods-admin.js',
				array('jquery', 'jquery-ui-sortable'),
				'1.0.0',
				true
			);

			// Localize the script with data needed for AJAX requests
			wp_localize_script('payop-payment-methods-admin', 'payop_admin_i18n', array(
				'ajax_url' => admin_url('admin-ajax.php'),
				'nonce' => wp_create_nonce('payop_refresh_payment_methods'),
				'missing_credentials' => __('Please enter your JWT Token and Project ID first.', 'payop-woocommerce'),
				'refresh_success' => __('Payment methods refreshed successfully.', 'payop-woocommerce'),
				'save_success' => __('Payment methods saved successfully.', 'payop-woocommerce'),
				'ajax_error' => __('Failed to communicate with server.', 'payop-woocommerce'),
				'generic_error' => __('An error occurred.', 'payop-woocommerce'),
				'no_methods' => __('No payment methods available.', 'payop-woocommerce'),
			));

			wp_enqueue_style(
				'payop-payment-methods-admin',
				PAYOP_PLUGIN_URL . 'css/payment-methods-admin.css',
				array(),
				'1.0.0'
			);
		}
	}

	/**
	 * Add custom form fields.
	 * 
	 * @param array $form_fields Current form fields.
	 * @return array Updated form fields.
	 */
	public function add_custom_form_fields($form_fields) {
		 // Use WooCommerce's format field to render our custom content
		if (isset($form_fields['payment_methods_manager'])) {
			// Custom HTML for payment methods manager
			$html = $this->generate_payment_methods_manager_html();
			
			// Custom field format that WooCommerce can handle
			$form_fields['payment_methods_manager'] = array(
				'title'       => __('Manage Payment Methods', 'payop-woocommerce'),
				'type'        => 'title', // This makes WC render just the title
				'description' => $html,
				'class'       => 'payop-payment-methods-section',
			);
		}
		
		return $form_fields;
	}

	/**
	 * Generate HTML for payment methods manager.
	 * 
	 * @return string HTML content.
	 */
	protected function generate_payment_methods_manager_html() {
		ob_start();
		?>
		<tr valign="top">
			<th scope="row" class="titledesc">
				<?php _e('Payment Methods', 'payop-woocommerce'); ?>
			</th>
			<td class="forminp">
				<div class="payop-payment-methods-wrapper">
					<?php if (empty($this->jwt_token) || empty($this->project_id)): ?>
					<div class="payop-credentials-notice">
						<p><strong><?php _e('Missing required credentials', 'payop-woocommerce'); ?></strong></p>
						<p><?php _e('To manage payment methods, you need to enter both your JWT token and Project ID in the API Credentials section above.', 'payop-woocommerce'); ?></p>
						<p class="payop-jwt-required"><?php _e('Generate a JWT token in your Payop account under Profile > Settings > JWT Tokens.', 'payop-woocommerce'); ?></p>
					</div>
					<?php endif; ?>
					
					<div class="payop-payment-methods-actions">
						<div class="payop-action-buttons">
							<button id="payop-refresh-methods" class="button button-secondary">
								<?php _e('Refresh Payment Methods', 'payop-woocommerce'); ?>
							</button>
							<button id="payop-select-all-methods" class="button button-secondary">
								<?php _e('Select All', 'payop-woocommerce'); ?>
							</button>
							<button id="payop-deselect-all-methods" class="button button-secondary">
								<?php _e('Deselect All', 'payop-woocommerce'); ?>
							</button>
							<span class="spinner payop-spinner"></span>
						</div>
						<div class="payop-selected-info">
							<span class="payop-selected-count">0 of 0</span>
							<button id="payop-save-methods" class="button button-primary" disabled>
								<?php _e('Save Selected Methods', 'payop-woocommerce'); ?>
							</button>
						</div>
					</div>
					<div class="payop-admin-notice"></div>
					<ul id="payop-payment-methods-list">
						<?php
						// Don't try to load payment methods if credentials are missing
						if (empty($this->jwt_token) || empty($this->project_id)) {
							echo '<p class="payop-payment-methods-empty">' . __('Enter your JWT token and Project ID, then click "Refresh Payment Methods" to fetch available payment methods.', 'payop-woocommerce') . '</p>';
						} else {
							$methods = $this->payment_methods_handler->get_available_payment_methods(
								$this->jwt_token,
								$this->project_id
							);
							
							if (isset($methods['error'])) {
								echo '<p class="payop-payment-methods-empty">' . esc_html($methods['error']) . '</p>';
							} elseif (empty($methods)) {
								echo '<p class="payop-payment-methods-empty">' . __('No payment methods available. Click "Refresh Payment Methods" to fetch the latest methods.', 'payop-woocommerce') . '</p>';
							} else {
								$selected_methods = $this->get_selected_payment_methods();
								
								foreach ($methods as $method) {
									$method_identifier = isset($method['identifier']) ? $method['identifier'] : $method['pmIdentifier'];
									$is_checked = in_array($method_identifier, $selected_methods) ? 'checked' : '';
									$currencies = isset($method['currencies']) ? implode(', ', $method['currencies']) : '';
									$countries = isset($method['countries']) ? implode(', ', $method['countries']) : '';
									
									?>
									<li class="payop-method-item" data-id="<?php echo esc_attr($method_identifier); ?>">
										<div class="payop-method-handle dashicons dashicons-menu"></div>
										<div class="payop-method-checkbox">
											<input type="checkbox" 
												   id="payop-method-<?php echo esc_attr($method_identifier); ?>" 
												   name="payop_selected_methods[]" 
												   value="<?php echo esc_attr($method_identifier); ?>" 
												   <?php echo $is_checked; ?>>
										</div>
										<div class="payop-method-logo">
											<img src="<?php echo esc_url($method['logo'] ?? ''); ?>" alt="<?php echo esc_attr($method['title']); ?>">
										</div>
										<div class="payop-method-details">
											<label for="payop-method-<?php echo esc_attr($method_identifier); ?>" class="payop-method-title">
												<?php echo esc_html($method['title']); ?>
											</label>
											<div class="payop-method-meta">
												<span class="payop-method-id">ID: <?php echo esc_html($method_identifier); ?></span>
												<?php if ($currencies) : ?>
													<span class="payop-method-currencies">Currencies: <?php echo esc_html($currencies); ?></span>
												<?php endif; ?>
												<?php if ($countries) : ?>
													<span class="payop-method-countries">Countries: <?php echo esc_html($countries); ?></span>
												<?php endif; ?>
											</div>
										</div>
									</li>
									<?php
								}
							}
						}
						?>
					</ul>
				</div>
			</td>
		</tr>
		<?php
		return ob_get_clean();
	}

	/**
	 * Generate payment form.
	 *
	 * @param int $order_id Order ID.
	 * @return string Form HTML or redirect.
	 */
	public function generate_form($order_id) {
		$order = wc_get_order($order_id);
		$response = $order->get_meta(PAYOP_INVITATE_RESPONSE);

		if (!$response) {
			$out_summ = number_format($order->get_total(), 4, '.', '');
			$currency = $order->get_currency();
			$site_url = get_site_url();

			$order_info = array(
				'id' => $order_id,
				'amount' => $out_summ,
				'currency' => $order->get_currency()
			);

			ksort($order_info, SORT_STRING);
			$data_set = array_values($order_info);
			$data_set[] = $this->secret_key;
			$signature = hash(PAYOP_HASH_ALGORITHM, implode(':', $data_set));

			// Get customer information - handle cases where only email is available
			$first_name = $order->get_billing_first_name();
			$last_name = $order->get_billing_last_name();
			$email = $order->get_billing_email();
			$phone = $order->get_billing_phone();
			
			// If no name is provided, try to extract from email or use generic name
			if (empty($first_name) && empty($last_name)) {
				if (!empty($email)) {
					$email_parts = explode('@', $email);
					$first_name = ucfirst($email_parts[0]);
				} else {
					$first_name = __('Customer', 'payop-woocommerce');
				}
			}
			
			// If no email, use a placeholder (though this shouldn't happen in WooCommerce)
			if (empty($email)) {
				$email = 'no-reply@' . parse_url(get_site_url(), PHP_URL_HOST);
			}

			$result_url = add_query_arg(
				array(
					'wc-api' => 'wc_payop',
					'payop'  => 'success',
					'orderId' => $order_id
				),
				$order->get_checkout_order_received_url()
			);

			$fail_path = add_query_arg(
				array(
					'wc-api' => 'wc_payop',
					'payop'  => 'fail',
					'orderId' => $order_id
				),
				$order->get_cancel_order_url()
			);

			$arr_data = array(
				'publicKey' => $this->public_key,
				'order' => array(
					'id' => strval($order_id),
					'amount' => $out_summ,
					'currency' => $currency,
					'description' => __('Payment order #', 'payop-woocommerce') . $order_id,
					'items' => array()
				),
				'payer' => array(
					'email' => $email,
					'name' => implode(' ', array_filter(array($first_name, $last_name))),
					'phone' => !empty($phone) ? $phone : ''
				),
				'language' => $this->language,
				'productUrl' => $site_url,
				'resultUrl' => $result_url,
				'failPath' => $fail_path,
				'signature' => $signature
			);

			// Add selected payment method if available
			$payment_method_id = $order->get_meta('_payop_payment_method_id');
			if (!empty($payment_method_id)) {
				$arr_data['paymentMethod'] = $payment_method_id;
			}

			$response = $this->api_request($arr_data, PAYOP_API_IDENTIFIER);
			$order->add_meta_data(PAYOP_INVITATE_RESPONSE, $response);
			$order->save_meta_data();
		}

		if (isset($response['messages'])) {
			return '<p>' . __('Request to payment service was sent incorrectly', 'payop-woocommerce') . '</p><br><p>' . $response['messages'] . '</p>';
		}

		$payment_method_id = $order->get_meta('_payop_payment_method_id');
		$base_url = 'https://checkout.payop.com/' . $this->language . '/payment/invoice-preprocessing/' . $response;

		// If direct redirect is enabled and we have a payment method ID
		if ($this->direct_redirect === 'yes' && !empty($payment_method_id)) {
			$action_adr = $base_url . '?method=' . $payment_method_id;
		} else {
			$action_adr = $base_url;
		}

		// Redirect immediately if skip confirm is enabled
		if ($this->skip_confirm === "yes") {
			wp_redirect(esc_url($action_adr));
			exit;
		}

		return $this->generate_payment_form_html($action_adr, $order);
	}

	/**
	 * Generates payment form HTML.
	 *
	 * @param string $action_adr The URL where the form should be submitted.
	 * @param WC_Order $order The WooCommerce order object.
	 * @return string The generated HTML for the payment form.
	 */
	private function generate_payment_form_html($action_adr, $order) {
		$form_args = array(
			'action' => esc_url($action_adr),
			'method' => 'GET',
			'id' => 'payop_payment_form'
		);

		$form_attributes = array_map(function($key, $value) {
			return $key . '="' . $value . '"';
		}, array_keys($form_args), $form_args);

		return '<form ' . implode(' ', $form_attributes) . '>' .
			'<input type="submit" class="button alt" id="submit_payop_payment_form" value="' . __('Pay', 'payop-woocommerce') . '" /> ' .
			'<a class="button cancel" href="' . esc_url($order->get_cancel_order_url()) . '">' . __('Refuse payment & return to cart', 'payop-woocommerce') . '</a>' .
			'</form>';
	}

	/**
	 * Add payment method to order meta.
	 * 
	 * @param int $order_id Order ID.
	 */
	public function add_payment_method_to_order($order_id) {
		if (isset($_POST['payop_payment_method'])) {
			$payment_method_id = sanitize_text_field($_POST['payop_payment_method']);
			update_post_meta($order_id, '_payop_payment_method_id', $payment_method_id);
		}
	}

	/**
	 * Get array of selected payment method IDs.
	 * 
	 * @return array Selected payment method IDs.
	 */
	public function get_selected_payment_methods() {
		$selected_methods_str = $this->get_option('selected_payment_methods', '');

		if (empty($selected_methods_str)) {
			return array();
		}

		return array_map('trim', explode(',', $selected_methods_str));
	}

	/**
	 * Admin Panel Options.
	 */
	public function admin_options() {
		?>
		<h3><?php _e('Payop', 'payop-woocommerce'); ?></h3>
		<p><?php _e('Take payments via Payop payment gateway.', 'payop-woocommerce'); ?></p>

		<?php if ($this->is_valid_for_use()) : ?>
			<table class="form-table">
				<?php $this->generate_settings_html(); ?>
			</table>
		<?php else : ?>
			<div class="inline error">
				<p>
					<strong><?php _e('Gateway Disabled', 'payop-woocommerce'); ?></strong>: 
					<?php _e('Payop does not support your store currency.', 'payop-woocommerce'); ?>
				</p>
			</div>
		<?php endif; ?>
		<?php
	}

	/**
	 * Payment fields displayed on the checkout page.
	 */
	public function payment_fields() {
		// Show description if available
		if ($this->description) {
			echo wpautop(wptexturize($this->description));
		}

		// Get the payment method ID - either from instance or from POST data
		$payment_method_id = '';
		if (isset($this->payment_method_id) && !empty($this->payment_method_id)) {
			$payment_method_id = $this->payment_method_id;
		} elseif (isset($_POST['payment_method']) && strpos($_POST['payment_method'], PAYOP_PAYMENT_GATEWAY_NAME . '_') === 0) {
			$parts = explode('_', sanitize_text_field($_POST['payment_method']), 2);
			if (isset($parts[1])) {
				$payment_method_id = $parts[1];
			}
		}

		// Always show the hidden field with payment method ID
		if (!empty($payment_method_id)) {
			echo '<input type="hidden" name="payop_payment_method" value="' . esc_attr($payment_method_id) . '">';
		}

		// Only show additional fields if this is a specific payment method instance
		if (!$this->is_payment_method_instance || empty($payment_method_id)) {
			return;
		}

		// Get required fields for this payment method
		$required_fields = array();

		// First try to get fields from stored payment method data if available
		if (isset($this->payment_method_data) && is_array($this->payment_method_data)) {
			$required_fields = WC_Payop_Payment_Method_Helper::get_required_fields($this->payment_method_data);
			if (function_exists('payop_debug')) {
				payop_debug("Got required fields from stored method data for payment method: $payment_method_id", 'info');
			}
		}

		// Fallback to API call if no stored data
		if (empty($required_fields)) {
			$api_fields = $this->payment_methods_handler->get_payment_method_required_fields(
				$this->jwt_token,
				$this->project_id,
				$payment_method_id
			);

			if (!isset($api_fields['error']) && !empty($api_fields)) {
				$required_fields = $api_fields;
				if (function_exists('payop_debug')) {
					payop_debug("Got required fields from API for payment method: $payment_method_id", 'info');
				}
			}
		}

		if (empty($required_fields)) {
			if (function_exists('payop_debug')) {
				payop_debug("No required fields found for payment method: $payment_method_id", 'info');
			}
			return;
		}

		if (function_exists('payop_debug')) {
			payop_debug("Displaying " . count($required_fields) . " required fields for payment method: $payment_method_id", 'info');
		}
		
		echo '<div class="payop-payment-fields" id="payop-fields-' . esc_attr($payment_method_id) . '">';

		foreach ($required_fields as $field) {
			// Handle both API format and helper format
			$field_name = isset($field['name']) ? $field['name'] : '';
			$field_type = isset($field['type']) ? $field['type'] : 'text';
			$field_label = isset($field['label']) ? $field['label'] : (isset($field['title']) ? $field['title'] : ucfirst(str_replace('_', ' ', $field_name)));
			$field_required = isset($field['required']) ? $field['required'] : false;
			$field_placeholder = isset($field['placeholder']) ? $field['placeholder'] : '';
			$field_pattern = isset($field['pattern']) ? $field['pattern'] : (isset($field['regexp']) ? $field['regexp'] : '');

			if (empty($field_name)) {
				continue;
			}

			$form_field_name = 'payop_field_' . $field_name;
			$form_field_id = 'payop_field_' . $payment_method_id . '_' . $field_name;

			echo '<p class="form-row form-row-wide">';
			echo '<label for="' . esc_attr($form_field_id) . '">' . esc_html($field_label);
			if ($field_required) {
				echo ' <span class="required">*</span>';
			}
			echo '</label>';

			// Handle special field types
			if ($field_type === 'bank_code' && !empty($field['options'])) {
				echo '<select id="' . esc_attr($form_field_id) . '" name="' . esc_attr($form_field_name) . '" class="select"';
				if ($field_required) {
					echo ' required';
				}
				echo '>';
				echo '<option value="">' . __('Please select...', 'payop-woocommerce') . '</option>';
				foreach ($field['options'] as $option) {
					$option_value = is_array($option) ? $option['value'] : $option;
					$option_label = is_array($option) ? $option['label'] : $option;
					echo '<option value="' . esc_attr($option_value) . '">' . esc_html($option_label) . '</option>';
				}
				echo '</select>';
			} elseif ($field_type === 'bank_type' && !empty($field_pattern)) {
				// For bank_type fields with patterns like "^(SEPA|SEPA_INSTANT)$"
				$options = array();
				if (preg_match('/\^\\(([^)]+)\\)\$/', $field_pattern, $matches)) {
					$options = explode('|', $matches[1]);
				}

				if (!empty($options)) {
					echo '<select id="' . esc_attr($form_field_id) . '" name="' . esc_attr($form_field_name) . '" class="select"';
					if ($field_required) {
						echo ' required';
					}
					echo '>';
					echo '<option value="">' . __('Please select...', 'payop-woocommerce') . '</option>';
					foreach ($options as $option) {
						echo '<option value="' . esc_attr($option) . '">' . esc_html($option) . '</option>';
					}
					echo '</select>';
				} else {
					// Fallback to text input
					echo '<input type="text" id="' . esc_attr($form_field_id) . '" name="' . esc_attr($form_field_name) . '" class="input-text"';
					if ($field_required) echo ' required';
					if ($field_placeholder) echo ' placeholder="' . esc_attr($field_placeholder) . '"';
					if ($field_pattern) echo ' pattern="' . esc_attr($field_pattern) . '"';
					echo ' />';
				}
			} else {
				// Regular input field
				$input_type = in_array($field_type, array('text', 'email', 'tel', 'number')) ? $field_type : 'text';
				echo '<input type="' . esc_attr($input_type) . '" id="' . esc_attr($form_field_id) . '" name="' . esc_attr($form_field_name) . '" class="input-text"';

				if ($field_required) {
					echo ' required';
				}

				if ($field_placeholder) {
					echo ' placeholder="' . esc_attr($field_placeholder) . '"';
				}

				if ($field_pattern) {
					echo ' pattern="' . esc_attr($field_pattern) . '"';
				}

				echo ' />';
			}
			echo '</p>';
		}

		echo '</div>';
		
		// Add some JavaScript to validate fields
		?>
		<script type="text/javascript">
		jQuery(document).ready(function($) {
			$('form.checkout').on('checkout_place_order_<?php echo esc_js($this->id); ?>', function() {
				var isValid = true;
				$('#payop-fields-<?php echo esc_js($payment_method_id); ?> [required]').each(function() {
					if (!$(this).val()) {
						isValid = false;
						$(this).focus();
						return false;
					}
				});
				
				if (!isValid) {
					alert('<?php echo esc_js(__('Please fill in all required payment fields.', 'payop-woocommerce')); ?>');
				}
				
				return isValid;
			});
		});
		</script>
		<?php
	}

	/**
	 * Check if direct integration should be used
	 *
	 * @return bool
	 */
	private function should_use_direct_integration() {
		// Direct integration is enabled by default for maximum user experience
		return apply_filters('payop_use_direct_integration', true);
	}

	/**
	 * Save payment method fields from POST data
	 *
	 * @param WC_Order $order The order object
	 * @param string $payment_method_id The payment method ID
	 */
	private function save_payment_method_fields($order, $payment_method_id) {
		$saved_fields = array();
		
		// Debug what POST data we received
		if (function_exists('payop_debug')) {
			payop_debug('POST data received for payment method fields:', 'info');
			foreach ($_POST as $key => $value) {
				if (strpos($key, 'payop_field_') === 0) {
					payop_debug("Found field: $key = $value", 'info');
				}
			}
		}
		
		// Look for payop_field_ prefixed POST data with validation
		foreach ($_POST as $key => $value) {
			if (strpos($key, 'payop_field_') === 0) {
				$field_name = str_replace('payop_field_', '', $key);
				$sanitized_value = $this->sanitize_payment_field($field_name, $value);
				$saved_fields[$field_name] = $sanitized_value;
			}
		}
		
		if (!empty($saved_fields)) {
			$order->update_meta_data('_payop_payment_method_fields', $saved_fields);
			$order->update_meta_data('_payop_selected_payment_method', $payment_method_id);
			$order->save_meta_data();
			
			if (function_exists('payop_debug')) {
				payop_debug("Saved payment method fields for order #{$order->get_id()}: " . print_r($saved_fields, true), 'info');
			}
		} else {
			if (function_exists('payop_debug')) {
				payop_debug("No payment method fields found in POST data for order #{$order->get_id()}", 'warning');
			}
		}
	}

	/**
	 * Process direct integration payment flow
	 *
	 * @param WC_Order $order The order object
	 * @param string $payment_method_id The payment method ID
	 * @return array|false Redirect result or false if failed
	 */
	private function process_direct_integration($order, $payment_method_id) {
		try {
			// Step 0: Validate currency support for this payment method
			$currency_validation = $this->validate_payment_method_currency($payment_method_id, $order->get_currency());
			
			if (!$currency_validation['valid']) {
				throw new Exception('Currency validation failed: ' . $currency_validation['error']);
			}
			
			if (function_exists('payop_debug')) {
				payop_debug("Currency validation passed for payment method $payment_method_id with currency " . $order->get_currency(), 'info');
			}
			
			// Step 1: Create invoice with specific payment method
			$invoice_data = $this->prepare_invoice_data($order, $payment_method_id);
			$invoice_response = $this->payment_methods_handler->create_invoice($this->jwt_token, $invoice_data);
			
			if (isset($invoice_response['error'])) {
				throw new Exception('Invoice creation failed: ' . $invoice_response['error']);
			}
			
			$invoice_id = isset($invoice_response['data']['id']) ? $invoice_response['data']['id'] : null;
			if (!$invoice_id) {
				throw new Exception('Invalid invoice response - missing invoice ID');
			}
			
			// Step 2: Prepare transaction data with payment method fields
			$transaction_data = $this->prepare_transaction_data($order, $invoice_id, $payment_method_id);
			
			// Step 3: Create checkout transaction
			$transaction_response = $this->payment_methods_handler->create_checkout_transaction($this->jwt_token, $transaction_data);
			
			if (isset($transaction_response['error'])) {
				throw new Exception('Transaction creation failed: ' . $transaction_response['error']);
			}
			
			// Get transaction ID from response - it's in 'txid' field according to API docs
			$transaction_id = isset($transaction_response['data']['txid']) ? $transaction_response['data']['txid'] : null;
			if (!$transaction_id) {
				// Fallback to 'id' field if 'txid' not found
				$transaction_id = isset($transaction_response['data']['id']) ? $transaction_response['data']['id'] : null;
			}
			
			if (!$transaction_id) {
				throw new Exception('Invalid transaction response - missing transaction ID. Response: ' . print_r($transaction_response, true));
			}
			
			// The checkout/create endpoint should return a redirect URL directly in some cases
			// But we need to check status to get the final redirect URL
			
			// Step 4: Check transaction status to get redirect URL or form data
			$status_response = $this->payment_methods_handler->check_transaction_status($this->jwt_token, $invoice_id);
			
			if (isset($status_response['error'])) {
				throw new Exception('Status check failed: ' . $status_response['error']);
			}
			
			$redirect_url = null;
			$form_data = null;
			
			// Check for direct URL redirect
			if (isset($status_response['data']['url']) && !empty($status_response['data']['url'])) {
				$redirect_url = $status_response['data']['url'];
			}
			// Check for form-based redirect (for 3D Secure, etc.)
			elseif (isset($status_response['data']['form']) && is_array($status_response['data']['form'])) {
				$form_data = $status_response['data']['form'];
				$redirect_url = isset($form_data['url']) ? $form_data['url'] : null;
			}
			// Sometimes the redirect URL is directly in redirectUrl
			elseif (isset($status_response['data']['redirectUrl'])) {
				$redirect_url = $status_response['data']['redirectUrl'];
			}
			
			if (!$redirect_url && !$form_data) {
				throw new Exception('No redirect URL or form data found in transaction status. Response: ' . print_r($status_response, true));
			}
			
			// Save transaction details to order
			$order->update_meta_data('_payop_invoice_id', $invoice_id);
			$order->update_meta_data('_payop_transaction_id', $transaction_id);
			$order->update_meta_data('_payop_direct_integration', 'yes');
			$order->save_meta_data();
			
			// If we have form data, we need to auto-submit it
			if ($form_data && isset($form_data['method']) && $form_data['method'] === 'POST') {
				// Create an auto-submit form page
				$form_html = $this->create_auto_submit_form($form_data, $order);
				
				// Return a receipt page that will auto-submit the form
				return array(
					'result' => 'success',
					'redirect' => $order->get_checkout_payment_url(true)
				);
			} else {
				// Return direct redirect to payment gateway
				return array(
					'result' => 'success',
					'redirect' => $redirect_url
				);
			}
			
		} catch (Exception $e) {
			if (function_exists('payop_debug')) {
				payop_debug("Direct integration failed for order #" . $order->get_id() . ": " . $e->getMessage(), 'error');
			}
			
			// Fall back to traditional flow
			wc_add_notice(__('Direct payment processing failed. Redirecting to payment page.', 'payop-woocommerce'), 'notice');
			return false;
		}
	}

	/**
	 * Prepare invoice data for API call
	 *
	 * @param WC_Order $order The order object
	 * @param string $payment_method_id Optional payment method ID for direct integration
	 * @return array Invoice data
	 */
	private function prepare_invoice_data($order, $payment_method_id = null) {
		$out_summ = number_format($order->get_total(), 4, '.', '');
		$currency = $order->get_currency();
		$order_id = $order->get_id();
		
		$order_info = array(
			'id' => $order_id,
			'amount' => $out_summ,
			'currency' => $currency
		);

		ksort($order_info, SORT_STRING);
		$data_set = array_values($order_info);
		$data_set[] = $this->secret_key;
		$signature = hash(PAYOP_HASH_ALGORITHM, implode(':', $data_set));

		// Get customer information - handle cases where only email is available
		$first_name = $order->get_billing_first_name();
		$last_name = $order->get_billing_last_name();
		$email = $order->get_billing_email();
		$phone = $order->get_billing_phone();
		
		// If no name is provided, try to extract from email or use generic name
		if (empty($first_name) && empty($last_name)) {
			if (!empty($email)) {
				$email_parts = explode('@', $email);
				$first_name = ucfirst($email_parts[0]);
			} else {
				$first_name = __('Customer', 'payop-woocommerce');
			}
		}
		
		// If no email, use a placeholder (though this shouldn't happen in WooCommerce)
		if (empty($email)) {
			$email = 'no-reply@' . parse_url(get_site_url(), PHP_URL_HOST);
		}

		$result_url = add_query_arg(
			array(
				'wc-api' => 'wc_payop',
				'payop'  => 'success',
				'orderId' => $order_id
			),
			$order->get_checkout_order_received_url()
		);

		$fail_path = add_query_arg(
			array(
				'wc-api' => 'wc_payop',
				'payop'  => 'fail',
				'orderId' => $order_id
			),
			$order->get_cancel_order_url()
		);

		$invoice_data = array(
			'publicKey' => $this->public_key,
			'order' => array(
				'id' => strval($order_id),
				'amount' => $out_summ,
				'currency' => $currency,
				'description' => __('Payment order #', 'payop-woocommerce') . $order_id,
				'items' => array()
			),
			'payer' => array(
				'email' => $email,
				'name' => implode(' ', array_filter(array($first_name, $last_name))),
				'phone' => !empty($phone) ? $phone : ''
			),
			'language' => $this->language,
			'productUrl' => get_site_url(),
			'resultUrl' => $result_url,
			'failPath' => $fail_path,
			'signature' => $signature
		);

		// Include payment method for direct integration if specified
		if (!empty($payment_method_id)) {
			$invoice_data['paymentMethod'] = intval($payment_method_id);
		}

		return $invoice_data;
	}

	/**
	 * Prepare transaction data for checkout API call
	 *
	 * @param WC_Order $order The order object
	 * @param string $invoice_id The invoice ID
	 * @param string $payment_method_id The payment method ID
	 * @return array Transaction data
	 */
	private function prepare_transaction_data($order, $invoice_id, $payment_method_id) {
		// Base transaction data according to PayOp API documentation
		$transaction_data = array(
			'invoiceIdentifier' => $invoice_id,
			'paymentMethod' => intval($payment_method_id), // PayOp expects this as integer
			'customer' => array(
				'email' => $order->get_billing_email(),
				'name' => trim($order->get_billing_first_name() . ' ' . $order->get_billing_last_name())
			)
		);
		
		// Add customer IP if available
		$customer_ip = $order->get_customer_ip_address();
		if (!empty($customer_ip)) {
			$transaction_data['customer']['ip'] = $customer_ip;
		}
		
		// Add checkStatusUrl for better user experience
		$transaction_data['checkStatusUrl'] = add_query_arg(
			array(
				'payop' => 'success',
				'orderId' => $order->get_id()
			),
			$order->get_checkout_order_received_url()
		);
		
		// Add payment method specific fields if they exist
		$payment_fields = $order->get_meta('_payop_payment_method_fields');
		if (!empty($payment_fields) && is_array($payment_fields)) {
			// For some payment methods like PIX in Brazil, add the document field
			if (isset($payment_fields['document'])) {
				$transaction_data['customer']['document'] = $payment_fields['document'];
			}
			
			// Add other fields as needed
			foreach ($payment_fields as $field_name => $field_value) {
				if ($field_name !== 'document') { // Already handled above
					$transaction_data['fields'][$field_name] = $field_value;
				}
			}
		}
		
		// Debug the transaction data being sent
		if (function_exists('payop_debug')) {
			payop_debug('Transaction data being sent to PayOp:', 'info');
			payop_debug('Payment Method ID: ' . $payment_method_id . ' (as integer: ' . intval($payment_method_id) . ')', 'info');
			payop_debug('Invoice ID: ' . $invoice_id, 'info');
			payop_debug('Customer data: ' . print_r($transaction_data['customer'], true), 'info');
			if (!empty($payment_fields)) {
				payop_debug('Payment fields: ' . print_r($payment_fields, true), 'info');
			}
			payop_debug('Full transaction data: ' . print_r($transaction_data, true), 'info');
		}
		
		return $transaction_data;
	}

	/**
	 * Create auto-submit form for POST-based redirects
	 *
	 * @param array $form_data Form data from PayOp API
	 * @param WC_Order $order The order object
	 * @return string HTML for auto-submit form
	 */
	private function create_auto_submit_form($form_data, $order) {
		$form_url = $form_data['url'];
		$form_method = isset($form_data['method']) ? strtoupper($form_data['method']) : 'POST';
		$form_fields = isset($form_data['fields']) ? $form_data['fields'] : array();
		
		// Store form data in order meta for use in receipt page
		$order->update_meta_data('_payop_form_data', $form_data);
		$order->save_meta_data();
		
		if (function_exists('payop_debug')) {
			payop_debug("Created auto-submit form for order #{$order->get_id()}: URL = $form_url", 'info');
		}
		
		return true; // We'll handle the form in receipt_page method
	}

	// Keep existing methods unchanged
	public function check_ipn_response() {
		// Handle IPN (Instant Payment Notification) responses from Payop
		global $woocommerce;
		
		if (function_exists('payop_debug')) {
			payop_debug('IPN Response received: ' . print_r($_REQUEST, true), 'info');
		}
		
		// Check if this is a valid request
		if (isset($_REQUEST['payop']) && isset($_REQUEST['orderId'])) {
			$payop_action = sanitize_text_field($_REQUEST['payop']);
			$order_id = intval($_REQUEST['orderId']);
			
			// Process different types of requests
			switch ($payop_action) {
				case 'success':
					$this->process_success_request($_REQUEST);
					break;
				case 'fail':
					$this->process_fail_request($_REQUEST);
					break;
				case 'result':
					$this->process_result_request($_REQUEST);
					break;
				default:
					$this->process_invalid_request();
					break;
			}
		} else {
			// Check for IPN data in POST body
			$posted_data = file_get_contents('php://input');
			if (!empty($posted_data)) {
				$json_data = json_decode($posted_data, true);
				if ($json_data && $this->check_ipn_request_is_valid($json_data)) {
					$this->successful_request($json_data);
				}
			}
		}
		
		exit();
	}

	/**
	 * Map PayOp transaction state to WooCommerce order status
	 *
	 * @param int $payop_state PayOp transaction state
	 * @return string WooCommerce order status
	 */
	private function map_status_to_wc($payop_state) {
		// Complete mapping according to PayOp API documentation
		$status_mapping = array(
			1 => 'processing',    // New (no actions taken)
			2 => 'processing',    // Accepted (paid successfully)
			3 => 'failed',        // Failed (technical/financial reasons)
			4 => 'pending',       // Pending (awaiting payment)
			5 => 'failed',        // Failed (technical/financial reasons)
			9 => 'on-hold',       // Pre-approved (submitted, awaiting funds)
			15 => 'failed',       // Timeout (lack of final confirmation)
		);

		$wc_status = isset($status_mapping[$payop_state]) ? $status_mapping[$payop_state] : 'pending';

		if (function_exists('payop_debug')) {
			payop_debug("Mapped PayOp state {$payop_state} to WooCommerce status: {$wc_status}", 'info');
		}

		return $wc_status;
	}

	private function process_result_request($posted_data) {
		// Process result callback from Payop (IPN)
		$order_id = isset($posted_data['orderId']) ? intval($posted_data['orderId']) : 0;
		$order = wc_get_order($order_id);
		
		if (!$order) {
			if (function_exists('payop_debug')) {
				payop_debug('Result request: Order not found - ' . $order_id, 'error');
			}
			return;
		}
		
		if (function_exists('payop_debug')) {
			payop_debug('Processing result request for order #' . $order_id, 'info');
		}
		
		// Validate the request
		if ($this->check_ipn_request_is_valid($posted_data)) {
			$this->successful_request($posted_data);
		} else {
			if (function_exists('payop_debug')) {
				payop_debug('Result request validation failed for order #' . $order_id, 'error');
			}
		}
	}

	private function process_success_request($posted_data) {
		// Process success redirect from Payop
		$order_id = isset($posted_data['orderId']) ? intval($posted_data['orderId']) : 0;
		$order = wc_get_order($order_id);
		
		if (!$order) {
			if (function_exists('payop_debug')) {
				payop_debug('Success request: Order not found - ' . $order_id, 'error');
			}
			wp_redirect(wc_get_page_permalink('shop'));
			exit;
		}
		
		if (function_exists('payop_debug')) {
			payop_debug('Processing success request for order #' . $order_id, 'info');
		}
		
		// Add order note
		$order->add_order_note(__('Customer returned from Payop payment page successfully.', 'payop-woocommerce'));
		
		// Empty cart
		$this->empty_cart();
		
		// Redirect to thank you page
		wp_redirect($order->get_checkout_order_received_url());
		exit;
	}

	private function process_fail_request($posted_data) {
		// Process failure redirect from Payop
		$order_id = isset($posted_data['orderId']) ? intval($posted_data['orderId']) : 0;
		$order = wc_get_order($order_id);
		
		if (!$order) {
			if (function_exists('payop_debug')) {
				payop_debug('Fail request: Order not found - ' . $order_id, 'error');
			}
			wp_redirect(wc_get_page_permalink('cart'));
			exit;
		}
		
		if (function_exists('payop_debug')) {
			payop_debug('Processing fail request for order #' . $order_id, 'info');
		}
		
		// Add order note and mark as failed
		$order->add_order_note(__('Payment failed. Customer returned from Payop payment page.', 'payop-woocommerce'));
		$order->update_status('failed', __('Payment failed via Payop.', 'payop-woocommerce'));
		
		// Add notice for customer
		wc_add_notice(__('Payment was not successful. Please try again.', 'payop-woocommerce'), 'error');
		
		// Redirect to checkout
		wp_redirect($order->get_cancel_order_url());
		exit;
	}

	private function process_invalid_request() {
		// Process invalid requests
		if (function_exists('payop_debug')) {
			payop_debug('Invalid request received: ' . print_r($_REQUEST, true), 'error');
		}
		
		// Redirect to shop page
		wp_redirect(wc_get_page_permalink('shop'));
		exit;
	}

	public function prevent_payment_for_failed_orders($needs_payment, $order, $valid_order_statuses) {
		// Prevent payment attempts for orders that have already failed with our gateway
		if ($order && $order->get_payment_method() === $this->id) {
			// Don't allow payment for failed or cancelled orders
			if ($order->has_status(array('failed', 'cancelled'))) {
				return false;
			}
		}
		
		return $needs_payment;
	}

	/**
	 * Get the real client IP address, accounting for proxies and load balancers
	 *
	 * @return string Client IP address
	 */
	private function get_client_ip() {
		// Check for various headers that might contain the real IP
		$ip_headers = array(
			'HTTP_CF_CONNECTING_IP',     // Cloudflare
			'HTTP_CLIENT_IP',            // Proxy
			'HTTP_X_FORWARDED_FOR',      // Load balancer/proxy
			'HTTP_X_FORWARDED',          // Proxy
			'HTTP_X_CLUSTER_CLIENT_IP',  // Cluster
			'HTTP_FORWARDED_FOR',        // Proxy
			'HTTP_FORWARDED',            // Proxy
			'REMOTE_ADDR'                // Standard
		);

		foreach ($ip_headers as $header) {
			if (!empty($_SERVER[$header])) {
				$ip = $_SERVER[$header];

				// Handle comma-separated list (X-Forwarded-For can contain multiple IPs)
				if (strpos($ip, ',') !== false) {
					$ip = trim(explode(',', $ip)[0]);
				}

				// Validate IP address
				if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
					return $ip;
				}
			}
		}

		// Fallback to REMOTE_ADDR even if it's private (for local testing)
		return isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
	}

	public function check_ipn_request_is_valid($posted) {
		// Validate IPN request from Payop

		// First, validate source IP address against PayOp whitelist
		$allowed_ips = array(
			'*************',
			'*************',
			'************',
			'*************'
		);

		$client_ip = $this->get_client_ip();

		if (!in_array($client_ip, $allowed_ips)) {
			if (function_exists('payop_debug')) {
				payop_debug("IPN validation failed: Invalid source IP address: {$client_ip}. Allowed IPs: " . implode(', ', $allowed_ips), 'error');
			}
			return false;
		}

		if (function_exists('payop_debug')) {
			payop_debug("IPN IP validation passed: {$client_ip}", 'info');
		}

		if (!isset($posted['invoice']) || !isset($posted['status'])) {
			if (function_exists('payop_debug')) {
				payop_debug('IPN validation failed: Missing required fields', 'error');
			}
			return false;
		}
		
		// Validate signature if present
		if (isset($posted['signature'])) {
			// IPN signature validation uses different structure than invoice creation
			// According to PayOp documentation, IPN signature should be validated differently
			$invoice_id = $posted['invoice']['id'] ?? '';
			$transaction_id = $posted['transaction']['id'] ?? '';
			$status = $posted['transaction']['state'] ?? $posted['status'] ?? '';
			$amount = $posted['invoice']['metadata']['amount'] ?? $posted['invoice']['amount'] ?? '';

			// Build signature data according to IPN format
			$signature_data = array(
				'invoice_id' => $invoice_id,
				'transaction_id' => $transaction_id,
				'status' => $status,
				'amount' => $amount
			);

			ksort($signature_data, SORT_STRING);
			$data_set = array_values($signature_data);
			$data_set[] = $this->secret_key;
			$expected_signature = hash(PAYOP_HASH_ALGORITHM, implode(':', $data_set));

			if (function_exists('payop_debug')) {
				payop_debug('IPN signature validation - Expected: ' . $expected_signature . ', Received: ' . $posted['signature'], 'debug');
				payop_debug('IPN signature data: ' . print_r($signature_data, true), 'debug');
			}

			if ($posted['signature'] !== $expected_signature) {
				if (function_exists('payop_debug')) {
					payop_debug('IPN validation failed: Invalid signature. Expected: ' . $expected_signature . ', Received: ' . $posted['signature'], 'error');
				}
				return false;
			}
		}
		
		if (function_exists('payop_debug')) {
			payop_debug('IPN validation successful', 'info');
		}
		
		return true;
	}

	public function successful_request($posted) {
		// Handle successful IPN request from Payop
		if (function_exists('payop_debug')) {
			payop_debug('Processing successful IPN request: ' . print_r($posted, true), 'info');
		}
		
		// Extract order ID from invoice data
		$order_id = 0;
		if (isset($posted['invoice']['txnId'])) {
			$order_id = intval($posted['invoice']['txnId']);
		} elseif (isset($posted['invoice']['id'])) {
			// Try to extract order ID from invoice ID
			$invoice_id = $posted['invoice']['id'];
			// If invoice ID contains order ID, extract it
			if (is_numeric($invoice_id)) {
				$order_id = intval($invoice_id);
			}
		}
		
		if (!$order_id) {
			if (function_exists('payop_debug')) {
				payop_debug('IPN error: Could not determine order ID from request', 'error');
			}
			return;
		}
		
		$order = wc_get_order($order_id);
		if (!$order) {
			if (function_exists('payop_debug')) {
				payop_debug('IPN error: Order not found - ' . $order_id, 'error');
			}
			return;
		}
		
		// Check if order is already completed
		if ($order->has_status(array('processing', 'completed'))) {
			if (function_exists('payop_debug')) {
				payop_debug('IPN: Order #' . $order_id . ' already processed', 'info');
			}
			return;
		}
		
		// Get payment status - handle both old and new IPN formats
		$payop_status = 0;
		if (isset($posted['transaction']['state'])) {
			$payop_status = intval($posted['transaction']['state']);
		} elseif (isset($posted['status'])) {
			$payop_status = intval($posted['status']);
		}

		$wc_status = $this->map_status_to_wc($payop_status);

		// Add transaction details to order
		if (isset($posted['transaction'])) {
			$transaction = $posted['transaction'];
			$order->update_meta_data('_payop_transaction_id', $transaction['id'] ?? '');
			$order->update_meta_data('_payop_transaction_status', $payop_status);

			// Add error message if present
			if (isset($transaction['error']['message'])) {
				$order->update_meta_data('_payop_error_message', sanitize_text_field($transaction['error']['message']));
			}

			// Add transaction fee if present
			if (isset($transaction['commission']) && $transaction['commission'] > 0) {
				$order->add_fee(__('Payment processing fee', 'payop-woocommerce'), $transaction['commission']);
			}
		}
		
		// Update order status and add notes based on PayOp transaction states
		switch ($payop_status) {
			case 2: // Accepted (paid successfully)
				$transaction_id = $posted['transaction']['id'] ?? 'N/A';
				$order->add_order_note(
					sprintf(__('Payment completed via Payop. Transaction ID: %s', 'payop-woocommerce'), $transaction_id)
				);
				$order->payment_complete($transaction_id);

				// Auto-complete order if enabled
				if ($this->auto_complete === 'yes' && $order->has_status('processing')) {
					$order->update_status('completed', __('Order auto-completed after successful payment.', 'payop-woocommerce'));
				}
				break;

			case 3: // Failed (technical/financial reasons)
			case 5: // Failed (technical/financial reasons)
			case 15: // Timeout (lack of final confirmation)
				$error_message = '';
				if (isset($posted['transaction']['error']['message'])) {
					$error_message = ' Error: ' . sanitize_text_field($posted['transaction']['error']['message']);
				}
				$order->add_order_note(
					sprintf(__('Payment failed via Payop. Status: %s%s', 'payop-woocommerce'), $payop_status, $error_message)
				);
				$order->update_status('failed', __('Payment failed.', 'payop-woocommerce'));
				break;

			case 4: // Pending (awaiting payment)
				$order->add_order_note(__('Payment is pending. Awaiting payment confirmation from Payop.', 'payop-woocommerce'));
				$order->update_status('pending', __('Payment pending.', 'payop-woocommerce'));
				break;

			case 9: // Pre-approved (submitted, awaiting funds)
				$order->add_order_note(__('Payment pre-approved. Awaiting funds confirmation from Payop.', 'payop-woocommerce'));
				$order->update_status('on-hold', __('Payment on hold.', 'payop-woocommerce'));
				break;

			case 1: // New (no actions taken) - should not normally receive IPN for this
			default: // Unknown status
				$order->add_order_note(
					sprintf(__('Payment status updated via Payop. Status: %s', 'payop-woocommerce'), $payop_status)
				);
				$order->update_status($wc_status);
				break;
		}
		
		$order->save();
		
		if (function_exists('payop_debug')) {
			payop_debug('IPN processed successfully for order #' . $order_id . ' with status: ' . $payop_status, 'info');
		}
	}

	/**
	 * Make API request to Payop with retry logic and better error handling.
	 *
	 * @param array $arr_data Request data.
	 * @param string $retrieved_header Optional header name to retrieve from response.
	 * @param int $max_retries Maximum number of retry attempts for transient errors.
	 * @return string|array Response from API or header value if $retrieved_header is provided.
	 */
	public function api_request($arr_data = [], $retrieved_header = '', $max_retries = 3) {
		if (function_exists('payop_debug')) {
			payop_debug('Making API request to: ' . $this->api_url, 'info');
			payop_debug('Request data: ' . print_r($arr_data, true), 'debug');
		}

		$args = array(
			'body'        => json_encode($arr_data),
			'timeout'     => '30',
			'redirection' => '5',
			'httpversion' => '1.0',
			'blocking'    => true,
			'headers'     => array(
				'Content-Type' => 'application/json',
			),
			'cookies'     => array()
		);

		$last_error = null;
		$attempt = 0;

		// Retry loop for transient errors
		while ($attempt <= $max_retries) {
			$attempt++;

			if ($attempt > 1) {
				// Wait before retry (exponential backoff)
				$wait_time = min(pow(2, $attempt - 2), 10); // Max 10 seconds
				if (function_exists('payop_debug')) {
					payop_debug("API request attempt $attempt/$max_retries after {$wait_time}s delay", 'info');
				}
				sleep($wait_time);
			}

			$response = wp_remote_post($this->api_url, $args);
		
			if (is_wp_error($response)) {
				$error_code = $response->get_error_code();
				$error_message = $response->get_error_message();
				$last_error = array(
					'type' => 'wp_error',
					'code' => $error_code,
					'message' => $error_message
				);

				if (function_exists('payop_debug')) {
					payop_debug("API request error (attempt $attempt): $error_code - $error_message", 'error');
				}

				// Check if this is a retryable error
				if ($this->is_retryable_error($error_code) && $attempt <= $max_retries) {
					continue; // Retry
				} else {
					// Non-retryable error or max retries reached
					return array(
						'error' => true,
						'messages' => $this->get_user_friendly_error_message($last_error),
						'technical_error' => $error_message
					);
				}
			}
		
			$response_code = wp_remote_retrieve_response_code($response);
			$response_body = wp_remote_retrieve_body($response);

			if (function_exists('payop_debug')) {
				payop_debug("API response code (attempt $attempt): " . $response_code, 'debug');
				payop_debug("API response body (attempt $attempt): " . $response_body, 'debug');

				// Log all response headers for debugging
				$headers = wp_remote_retrieve_headers($response);
				if ($headers) {
					payop_debug('API response headers: ' . print_r($headers, true), 'debug');
				}
			}

			// Check for retryable HTTP status codes
			if ($this->is_retryable_http_code($response_code) && $attempt <= $max_retries) {
				$last_error = array(
					'type' => 'http_error',
					'code' => $response_code,
					'message' => $response_body
				);
				continue; // Retry
			}
		
			// If we're looking for a specific header (like 'identifier')
			if (!empty($retrieved_header)) {
				$header_value = wp_remote_retrieve_header($response, $retrieved_header);

				if (!empty($header_value)) {
					if (function_exists('payop_debug')) {
						payop_debug("Found header '$retrieved_header': $header_value", 'info');
					}
					return $header_value; // Success, exit retry loop
				} else {
					if (function_exists('payop_debug')) {
						payop_debug("Header '$retrieved_header' not found in response", 'error');
					}
					$last_error = array(
						'type' => 'missing_header',
						'code' => 'header_not_found',
						'message' => "Cannot find $retrieved_header in API response"
					);
					// Don't retry for missing headers
					break;
				}
			}

			// If we're not looking for a specific header, return the body
			if ($response_code == 200) {
				$decoded_response = json_decode($response_body, true);
				if (json_last_error() === JSON_ERROR_NONE) {
					return $decoded_response; // Success, exit retry loop
				} else {
					$last_error = array(
						'type' => 'json_error',
						'code' => 'invalid_json',
						'message' => 'Invalid JSON response from API'
					);
					// Don't retry for JSON errors
					break;
				}
			} else {
				if (function_exists('payop_debug')) {
					payop_debug("API error response (attempt $attempt): HTTP $response_code - $response_body", 'error');
				}
				$last_error = array(
					'type' => 'http_error',
					'code' => $response_code,
					'message' => $response_body
				);
				// Continue to retry logic check
			}
		}

		// If we reach here, all retries failed
		if (function_exists('payop_debug')) {
			payop_debug('All API request attempts failed. Last error: ' . print_r($last_error, true), 'error');
		}

		return array(
			'error' => true,
			'messages' => $this->get_user_friendly_error_message($last_error),
			'technical_error' => $last_error['message'] ?? 'Unknown error'
		);
	}

	/**
	 * Check if an error is retryable
	 *
	 * @param string $error_code Error code
	 * @return bool True if retryable, false otherwise
	 */
	private function is_retryable_error($error_code) {
		$retryable_errors = array(
			'http_request_failed',
			'connect_error',
			'timeout',
			'ssl_error',
			'dns_error'
		);

		return in_array($error_code, $retryable_errors);
	}

	/**
	 * Check if an HTTP status code is retryable
	 *
	 * @param int $http_code HTTP status code
	 * @return bool True if retryable, false otherwise
	 */
	private function is_retryable_http_code($http_code) {
		$retryable_codes = array(
			500, // Internal Server Error
			502, // Bad Gateway
			503, // Service Unavailable
			504, // Gateway Timeout
			429  // Too Many Requests
		);

		return in_array($http_code, $retryable_codes);
	}

	/**
	 * Get user-friendly error message
	 *
	 * @param array $error Error details
	 * @return string User-friendly error message
	 */
	private function get_user_friendly_error_message($error) {
		if (!is_array($error)) {
			return __('An unexpected error occurred. Please try again.', 'payop-woocommerce');
		}

		$type = $error['type'] ?? 'unknown';
		$code = $error['code'] ?? '';

		switch ($type) {
			case 'wp_error':
				switch ($code) {
					case 'http_request_failed':
						return __('Unable to connect to payment service. Please check your internet connection and try again.', 'payop-woocommerce');
					case 'timeout':
						return __('Payment service is taking too long to respond. Please try again in a few moments.', 'payop-woocommerce');
					case 'ssl_error':
						return __('Secure connection error. Please contact support if this persists.', 'payop-woocommerce');
					default:
						return __('Connection error. Please try again or contact support.', 'payop-woocommerce');
				}

			case 'http_error':
				$http_code = intval($code);
				switch ($http_code) {
					case 400:
						return __('Invalid payment information. Please check your details and try again.', 'payop-woocommerce');
					case 401:
						return __('Payment service authentication failed. Please contact support.', 'payop-woocommerce');
					case 403:
						return __('Payment method not allowed. Please try a different payment method.', 'payop-woocommerce');
					case 404:
						return __('Payment service not found. Please contact support.', 'payop-woocommerce');
					case 429:
						return __('Too many payment attempts. Please wait a moment and try again.', 'payop-woocommerce');
					case 500:
					case 502:
					case 503:
					case 504:
						return __('Payment service is temporarily unavailable. Please try again in a few minutes.', 'payop-woocommerce');
					default:
						return sprintf(__('Payment service error (Code: %d). Please try again or contact support.', 'payop-woocommerce'), $http_code);
				}

			case 'json_error':
				return __('Invalid response from payment service. Please try again or contact support.', 'payop-woocommerce');

			case 'missing_header':
				return __('Incomplete response from payment service. Please try again or contact support.', 'payop-woocommerce');

			default:
				return __('An unexpected error occurred. Please try again or contact support.', 'payop-woocommerce');
		}
	}

	/**
	 * Check if this gateway is valid for use.
	 *
	 * @return bool
	 */
	public function is_valid_for_use() {
		// Get the currency being used in the store
		$currency = get_woocommerce_currency();
		
		// Define supported currencies for Payop
		// Adding common currencies including USD
		$supported_currencies = array(
			'USD', 'EUR', 'GBP', 'RUB', 'UAH', 'KZT', 'BYN', 
			'AUD', 'CAD', 'PLN', 'CZK', 'TRY', 'SEK', 'NOK', 
			'DKK', 'CHF', 'ZAR', 'INR', 'JPY', 'CNY'
		);
		
		// Check if the store's currency is supported by Payop
		return in_array($currency, $supported_currencies);
	}

	/**
	 * Initialize form fields that will be displayed in the admin settings.
	 */
	public function init_form_fields() {
		$this->form_fields = array(
			'enabled' => array(
				'title'       => __('Enable/Disable', 'payop-woocommerce'),
				'type'        => 'checkbox',
				'label'       => __('Enable Payop Payment Gateway', 'payop-woocommerce'),
				'default'     => 'yes',
			),
			'title' => array(
				'title'       => __('Title', 'payop-woocommerce'),
				'type'        => 'text',
				'description' => __('This controls the title which the user sees during checkout.', 'payop-woocommerce'),
				'default'     => __('Payop', 'payop-woocommerce'),
				'desc_tip'    => true,
			),
			'description' => array(
				'title'       => __('Description', 'payop-woocommerce'),
				'type'        => 'textarea',
				'description' => __('This controls the description which the user sees during checkout.', 'payop-woocommerce'),
				'default'     => __('Pay securely via Payop.', 'payop-woocommerce'),
				'desc_tip'    => true,
			),
			'instructions' => array(
				'title'       => __('Instructions', 'payop-woocommerce'),
				'type'        => 'textarea',
				'description' => __('Instructions that will be added to the thank you page.', 'payop-woocommerce'),
				'default'     => __('Thank you for your purchase. Please proceed with payment.', 'payop-woocommerce'),
				'desc_tip'    => true,
			),
			'api_details' => array(
				'title'       => __('API Credentials', 'payop-woocommerce'),
				'type'        => 'title',
				'description' => __('Enter your Payop API credentials to process payments via Payop.', 'payop-woocommerce'),
			),
			'public_key' => array(
				'title'       => __('Public Key', 'payop-woocommerce'),
				'type'        => 'text',
				'description' => __('Get your API credentials from Payop merchant dashboard.', 'payop-woocommerce'),
				'desc_tip'    => true,
			),
			'secret_key' => array(
				'title'       => __('Secret Key', 'payop-woocommerce'),
				'type'        => 'password',
				'description' => __('Get your API credentials from Payop merchant dashboard. This field is encrypted for security.', 'payop-woocommerce'),
				'desc_tip'    => true,
				'placeholder' => !empty($this->get_secure_option('secret_key')) ? '••••••••••••••••' : '',
			),
			'project_id' => array(
				'title'       => __('Project ID', 'payop-woocommerce'),
				'type'        => 'text',
				'description' => __('Get your Project ID from Payop merchant dashboard.', 'payop-woocommerce'),
				'desc_tip'    => true,
			),
			 'jwt_token' => array(
				'title'       => __('JWT Token', 'payop-woocommerce'),
				'type'        => 'password',
				'description' => __('Enter your JWT token for Payop API authentication. This field is encrypted for security.', 'payop-woocommerce'),
				'desc_tip'    => true,
				'placeholder' => !empty($this->get_secure_option('jwt_token')) ? '••••••••••••••••' : '',
			),
			'payment_options' => array(
				'title'       => __('Payment Options', 'payop-woocommerce'),
				'type'        => 'title',
				'description' => __('Customize the payment experience for your customers.', 'payop-woocommerce'),
			),
			'skip_confirm' => array(
				'title'       => __('Skip Confirmation', 'payop-woocommerce'),
				'type'        => 'checkbox',
				'label'       => __('Skip the confirmation page and redirect directly to Payop', 'payop-woocommerce'),
				'default'     => 'no',
			),
			'auto_complete' => array(
				'title'       => __('Auto Complete Orders', 'payop-woocommerce'),
				'type'        => 'checkbox',
				'label'       => __('Automatically mark orders as "completed" after successful payment', 'payop-woocommerce'),
				'default'     => 'no',
			),
			'payment_methods_section' => array(
				'title'       => __('Payment Methods', 'payop-woocommerce'),
				'type'        => 'title',
				'description' => __('Configure the payment methods that will be available to your customers.', 'payop-woocommerce'),
			),
			'show_payment_logos' => array(
				'title'       => __('Show Payment Logos', 'payop-woocommerce'),
				'type'        => 'checkbox',
				'label'       => __('Display payment method logos at checkout', 'payop-woocommerce'),
				'default'     => 'yes',
			),
			'direct_redirect' => array(
				'title'       => __('Direct Payment Method Redirect', 'payop-woocommerce'),
				'type'        => 'checkbox',
				'label'       => __('Redirect customers directly to the selected payment method page', 'payop-woocommerce'),
				'default'     => 'yes',
			),
			'selected_payment_methods' => array(
				'type'        => 'hidden',
				'default'     => '',
			),
			'payment_methods_manager' => array(
				'title'       => __('Manage Payment Methods', 'payop-woocommerce'),
				'type'        => 'payment_methods_manager',
				'description' => __('Select and order the payment methods you want to offer to your customers.', 'payop-woocommerce'),
			),
		);
	}

	/**
	 * Empty the cart after successful payment to prevent duplicate orders
	 */
	public function empty_cart() {
		// Empty the customer's cart after successful payment processing
		if (WC()->cart) {
			WC()->cart->empty_cart();
		}
	}

	public function hide_pay_button_for_failed_orders($actions, $order) {
		// Hide the "Pay" button for failed orders using our payment gateway
		if ($order && $order->get_payment_method() === $this->id) {
			if ($order->has_status(array('failed', 'cancelled'))) {
				// Remove the pay action for failed/cancelled orders
				if (isset($actions['pay'])) {
					unset($actions['pay']);
				}
			}
		}
		return $actions;
	}

	/**
	 * Modify the content of the WooCommerce order confirmation status block.
	 *
	 * @param string $block_content The content of the block.
	 * @param array $block The block data.
	 * @return string Modified block content.
	 */
	public function modify_wc_order_confirmation_block_content($block_content, $block) {
		// Make sure we always have a string value to return
		if ($block_content === null) {
			return '';
		}
		
		// Only process order confirmation status blocks
		if (isset($block['blockName']) && $block['blockName'] === 'woocommerce/order-confirmation-status') {
			// Remove the "Pay" link from failed orders with our payment gateway
			$pattern = '/<a[^>]*\bhref="([^"]*?pay_for_order=true[^"]*)"[^>]*>.*?<\/a>/i';
			if (preg_match($pattern, $block_content, $matches)) {
				$block_content = preg_replace($pattern, '', $block_content);
			}
		}
		
		return $block_content;
	}

	/**
	 * Display receipt page after successful payment.
	 *
	 * @param int $order_id Order ID.
	 */
	public function receipt_page($order_id) {
		$order = wc_get_order($order_id);
		
		if (function_exists('payop_debug')) {
			payop_debug("Processing receipt page for order #$order_id with gateway ID: {$this->id}", 'info');
		}
		
		// Check if we have form data that needs to be auto-submitted
		$form_data = $order->get_meta('_payop_form_data');
		if (!empty($form_data) && is_array($form_data)) {
			// Display auto-submit form for POST-based redirects
			$this->display_auto_submit_form($form_data);
			return;
		}
		
		if (!$order->get_meta(PAYOP_INVITATE_RESPONSE) || $order->has_status('pending')) {
			echo '<p>' . __('Thank you for your order, please click the button below to pay', 'payop-woocommerce') . '</p>';
			echo $this->generate_form($order_id);
		} else {
			$this->empty_cart();
		}
	}

	/**
	 * Display auto-submit form for POST-based redirects
	 *
	 * @param array $form_data Form data from PayOp API
	 */
	private function display_auto_submit_form($form_data) {
		$form_url = $form_data['url'];
		$form_method = isset($form_data['method']) ? strtoupper($form_data['method']) : 'POST';
		$form_fields = isset($form_data['fields']) ? $form_data['fields'] : array();
		
		echo '<p>' . __('Redirecting to payment gateway...', 'payop-woocommerce') . '</p>';
		echo '<form id="payop-redirect-form" action="' . esc_url($form_url) . '" method="' . esc_attr($form_method) . '">';
		
		foreach ($form_fields as $field_name => $field_value) {
			echo '<input type="hidden" name="' . esc_attr($field_name) . '" value="' . esc_attr($field_value) . '" />';
		}
		
		echo '<input type="submit" class="button" value="' . __('Continue to Payment', 'payop-woocommerce') . '" />';
		echo '</form>';
		
		// Auto-submit the form after a short delay
		?>
		<script type="text/javascript">
		setTimeout(function() {
			document.getElementById('payop-redirect-form').submit();
		}, 1000);
		</script>
		<?php
	}

	/**
	 * Enqueue checkout assets
	 */
	public function enqueue_checkout_assets() {
		if (is_checkout()) {
			wp_enqueue_script(
				'payop-payment-methods-checkout',
				PAYOP_PLUGIN_URL . 'js/payment-methods-checkout.js',
				array('jquery'),
				'1.0.0',
				true
			);
			
			wp_enqueue_style(
				'payop-payment-methods-checkout',
				array(),
				'1.0.0'
			);
			
			// Localize script with AJAX data
			wp_localize_script('payop-payment-methods-checkout', 'payop_checkout_params', array(
				'ajax_url' => admin_url('admin-ajax.php'),
				'nonce' => wp_create_nonce('payop_checkout_nonce')
			));
		}
	}

	/**
	 * AJAX handler for getting payment method fields
	 */
	public function ajax_get_payment_method_fields() {
		check_ajax_referer('payop_checkout_nonce', 'security');

		$payment_method_id = sanitize_text_field($_POST['payment_method_id'] ?? '');
		
		if (empty($payment_method_id)) {
			wp_send_json_error(array('message' => __('Payment method ID is required.', 'payop-woocommerce')));
		}

		$required_fields = $this->payment_methods_handler->get_payment_method_required_fields(
			$this->jwt_token,
			$this->project_id,
			$payment_method_id
		);

		if (isset($required_fields['error'])) {
			wp_send_json_error(array('message' => $required_fields['error']));
		}

		wp_send_json_success($required_fields);
	}

	/**
	 * Display currency support notice in admin
	 */
	public function display_currency_support_notice() {
		if (!current_user_can('manage_woocommerce')) {
			return;
		}
		
		$store_currency = get_woocommerce_currency();
		
		// Get all available payment methods
		$available_methods = $this->payment_methods_handler->get_available_payment_methods(
			$this->jwt_token,
			$this->project_id
		);

		if (!is_array($available_methods) || isset($available_methods['error'])) {
			return;
		}

		// Filter by currency
		$supported_methods = $this->payment_methods_handler->filter_payment_methods_by_currency($available_methods, $store_currency);
		
		if (count($supported_methods) < count($available_methods)) {
			$unsupported_count = count($available_methods) - count($supported_methods);
			echo '<div class="notice notice-warning"><p>';
			printf(
				__('%d payment methods do not support your store currency (%s) and will not be available to customers.', 'payop-woocommerce'),
				$unsupported_count,
				$store_currency
			);
			echo '</p></div>';
		}
	}

	/**
	 * Validate currency support for payment method before processing
	 *
	 * @param string $payment_method_id Payment method ID
	 * @param string $currency Currency code
	 * @return array Validation result
	 */
	private function validate_payment_method_currency($payment_method_id, $currency) {
		// Get available payment methods
		$available_methods = $this->payment_methods_handler->get_available_payment_methods(
			$this->jwt_token, 
			$this->project_id
		);
		
		if (isset($available_methods['error'])) {
			return array(
				'valid' => false,
				'error' => 'Cannot validate currency - API error: ' . $available_methods['error']
			);
		}
		
		// Find the specific payment method using helper
		$method_found = null;
		foreach ($available_methods as $method) {
			$method_id = WC_Payop_Payment_Method_Helper::get_method_identifier($method);
			if ($method_id == $payment_method_id) {
				$method_found = $method;
				break;
			}
		}

		if (!$method_found) {
			return array(
				'valid' => false,
				'error' => sprintf(__('Payment method ID %s not found or not available', 'payop-woocommerce'), $payment_method_id)
			);
		}

		// Use helper to check currency support
		$is_supported = WC_Payop_Payment_Method_Helper::supports_currency($method_found, $currency);
		$supported_currencies = isset($method_found['currencies']) ?
			array_map('strtoupper', $method_found['currencies']) : array();
		$currency = strtoupper($currency);

		if (function_exists('payop_debug')) {
			payop_debug(
				"Currency validation for payment method '{$method_found['title']}' (ID: $payment_method_id): " .
				"Requested=$currency, Supported=" . implode(',', $supported_currencies) .
				", Valid=" . ($is_supported ? 'YES' : 'NO'),
				$is_supported ? 'info' : 'warning'
			);
		}
		
		return array(
			'valid' => $is_supported,
			'method_name' => $method_found['title'],
			'supported_currencies' => $supported_currencies,
			'requested_currency' => $currency,
			'error' => $is_supported ? null : 
				"Payment method '{$method_found['title']}' does not support currency $currency. Supported: " . 
				implode(', ', $supported_currencies)
		);
	}
}
