<?php
/**
 * PayOp Payment Method Helper
 *
 * Provides consistent handling of payment method IDs and validation
 *
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
	exit;
}

/**
 * Class WC_Payop_Payment_Method_Helper
 *
 * Handles consistent payment method ID processing and validation
 */
class WC_Payop_Payment_Method_Helper {

	/**
	 * Normalize payment method ID to integer
	 *
	 * @param mixed $method_id Payment method ID
	 * @return int Normalized payment method ID
	 */
	public static function normalize_method_id($method_id) {
		if (empty($method_id)) {
			return 0;
		}
		
		// Handle string IDs that might contain non-numeric characters
		$method_id = preg_replace('/[^0-9]/', '', $method_id);
		
		return intval($method_id);
	}

	/**
	 * Validate payment method ID
	 *
	 * @param mixed $method_id Payment method ID
	 * @return bool True if valid, false otherwise
	 */
	public static function is_valid_method_id($method_id) {
		$normalized_id = self::normalize_method_id($method_id);
		return $normalized_id > 0;
	}

	/**
	 * Extract payment method ID from gateway ID
	 *
	 * @param string $gateway_id Gateway ID (e.g., 'payop_123')
	 * @return int Payment method ID
	 */
	public static function extract_method_id_from_gateway_id($gateway_id) {
		if (strpos($gateway_id, PAYOP_PAYMENT_GATEWAY_NAME . '_') === 0) {
			$parts = explode('_', $gateway_id, 2);
			if (isset($parts[1])) {
				return self::normalize_method_id($parts[1]);
			}
		}
		return 0;
	}

	/**
	 * Generate gateway ID from payment method ID
	 *
	 * @param int $method_id Payment method ID
	 * @return string Gateway ID
	 */
	public static function generate_gateway_id($method_id) {
		$normalized_id = self::normalize_method_id($method_id);
		if ($normalized_id > 0) {
			return PAYOP_PAYMENT_GATEWAY_NAME . '_' . $normalized_id;
		}
		return PAYOP_PAYMENT_GATEWAY_NAME;
	}

	/**
	 * Get payment method identifier from method data
	 *
	 * @param array $method_data Payment method data from API
	 * @return int Payment method identifier
	 */
	public static function get_method_identifier($method_data) {
		if (!is_array($method_data)) {
			return 0;
		}
		
		// Handle different identifier field names
		$identifier = 0;
		if (isset($method_data['identifier'])) {
			$identifier = $method_data['identifier'];
		} elseif (isset($method_data['pmIdentifier'])) {
			$identifier = $method_data['pmIdentifier'];
		} elseif (isset($method_data['id'])) {
			$identifier = $method_data['id'];
		}
		
		return self::normalize_method_id($identifier);
	}

	/**
	 * Validate payment method data structure
	 *
	 * @param array $method_data Payment method data
	 * @return array Validation result with 'valid' boolean and 'errors' array
	 */
	public static function validate_method_data($method_data) {
		$errors = array();
		
		if (!is_array($method_data)) {
			$errors[] = 'Payment method data must be an array';
			return array('valid' => false, 'errors' => $errors);
		}
		
		// Check required fields
		$required_fields = array('title', 'currencies', 'countries');
		foreach ($required_fields as $field) {
			if (!isset($method_data[$field])) {
				$errors[] = "Missing required field: {$field}";
			}
		}
		
		// Validate identifier
		$identifier = self::get_method_identifier($method_data);
		if ($identifier <= 0) {
			$errors[] = 'Invalid or missing payment method identifier';
		}
		
		// Validate currencies array
		if (isset($method_data['currencies']) && !is_array($method_data['currencies'])) {
			$errors[] = 'Currencies must be an array';
		}
		
		// Validate countries array
		if (isset($method_data['countries']) && !is_array($method_data['countries'])) {
			$errors[] = 'Countries must be an array';
		}
		
		return array(
			'valid' => empty($errors),
			'errors' => $errors
		);
	}

	/**
	 * Format payment method for display
	 *
	 * @param array $method_data Payment method data
	 * @return array Formatted method data
	 */
	public static function format_method_for_display($method_data) {
		$validation = self::validate_method_data($method_data);
		if (!$validation['valid']) {
			return null;
		}
		
		$identifier = self::get_method_identifier($method_data);
		
		return array(
			'id' => $identifier,
			'identifier' => $identifier,
			'title' => sanitize_text_field($method_data['title']),
			'logo' => isset($method_data['logo']) ? esc_url($method_data['logo']) : '',
			'type' => isset($method_data['type']) ? sanitize_text_field($method_data['type']) : '',
			'currencies' => array_map('strtoupper', $method_data['currencies']),
			'countries' => array_map('strtoupper', $method_data['countries']),
			'config' => isset($method_data['config']) ? $method_data['config'] : array()
		);
	}

	/**
	 * Check if payment method supports currency
	 *
	 * @param array $method_data Payment method data
	 * @param string $currency Currency code
	 * @return bool True if supported, false otherwise
	 */
	public static function supports_currency($method_data, $currency) {
		if (!is_array($method_data) || empty($currency)) {
			return false;
		}
		
		$currencies = isset($method_data['currencies']) ? $method_data['currencies'] : array();
		$currencies = array_map('strtoupper', $currencies);
		$currency = strtoupper($currency);
		
		return in_array($currency, $currencies);
	}

	/**
	 * Check if payment method supports country
	 *
	 * @param array $method_data Payment method data
	 * @param string $country Country code
	 * @return bool True if supported, false otherwise
	 */
	public static function supports_country($method_data, $country) {
		if (!is_array($method_data) || empty($country)) {
			return false;
		}
		
		$countries = isset($method_data['countries']) ? $method_data['countries'] : array();
		$countries = array_map('strtoupper', $countries);
		$country = strtoupper($country);
		
		return in_array($country, $countries);
	}

	/**
	 * Get required fields for payment method
	 *
	 * @param array $method_data Payment method data
	 * @return array Required fields configuration
	 */
	public static function get_required_fields($method_data) {
		if (!is_array($method_data)) {
			return array();
		}
		
		$config = isset($method_data['config']) ? $method_data['config'] : array();
		$fields = isset($config['fields']) ? $config['fields'] : array();
		
		$required_fields = array();
		foreach ($fields as $field) {
			if (isset($field['required']) && $field['required']) {
				$required_fields[] = $field;
			}
		}
		
		return $required_fields;
	}

	/**
	 * Validate field value against field configuration
	 *
	 * @param mixed $value Field value
	 * @param array $field_config Field configuration
	 * @return array Validation result with 'valid' boolean and 'error' string
	 */
	public static function validate_field_value($value, $field_config) {
		if (!is_array($field_config)) {
			return array('valid' => false, 'error' => 'Invalid field configuration');
		}
		
		$field_name = isset($field_config['name']) ? $field_config['name'] : 'unknown';
		$is_required = isset($field_config['required']) ? $field_config['required'] : false;
		$field_type = isset($field_config['type']) ? $field_config['type'] : 'text';
		$pattern = isset($field_config['regexp']) ? $field_config['regexp'] : '';
		
		// Check if required field is empty
		if ($is_required && empty($value)) {
			return array('valid' => false, 'error' => "Field '{$field_name}' is required");
		}
		
		// Skip validation if field is empty and not required
		if (empty($value) && !$is_required) {
			return array('valid' => true, 'error' => '');
		}
		
		// Type-specific validation
		switch ($field_type) {
			case 'email':
				if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
					return array('valid' => false, 'error' => "Field '{$field_name}' must be a valid email address");
				}
				break;
				
			case 'number':
				if (!is_numeric($value)) {
					return array('valid' => false, 'error' => "Field '{$field_name}' must be a number");
				}
				break;
		}
		
		// Pattern validation
		if (!empty($pattern) && !preg_match('/' . $pattern . '/', $value)) {
			return array('valid' => false, 'error' => "Field '{$field_name}' format is invalid");
		}
		
		return array('valid' => true, 'error' => '');
	}
}
