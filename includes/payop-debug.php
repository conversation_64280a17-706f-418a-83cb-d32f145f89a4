<?php
/**
 * Payop Debug Helper Functions
 *
 * @version 1.0.0
 */

if (!defined('ABSPATH')) {
	exit;
}

/**
 * Debug logging function
 *
 * @param string $message The message to log
 * @param string $level The log level (info, warning, error)
 */
if (!function_exists('payop_debug')) {
	function payop_debug($message, $level = 'info') {
		// Only log if WP_DEBUG is enabled
		if (!defined('WP_DEBUG') || !WP_DEBUG) {
			return;
		}
		
		$timestamp = current_time('mysql');
		$formatted_message = "[{$timestamp}] PAYOP [{$level}]: {$message}";
		
		// Log to WordPress debug log
		error_log($formatted_message);
		
		// Also log to custom PayOp log file if possible
		$upload_dir = wp_upload_dir();
		$log_file = $upload_dir['basedir'] . '/payop-debug.log';
		
		if (is_writable(dirname($log_file))) {
			file_put_contents($log_file, $formatted_message . "\n", FILE_APPEND | LOCK_EX);
		}
	}
}

/**
 * Log API request details
 *
 * @param string $endpoint The API endpoint
 * @param array $data The request data
 * @param array $response The API response
 */
function payop_debug_api_request($endpoint, $data = array(), $response = array()) {
	if (!defined('WP_DEBUG') || !WP_DEBUG) {
		return;
	}
	
	$message = "API Request to {$endpoint}";
	
	if (!empty($data)) {
		$message .= " | Data: " . wp_json_encode($data);
	}
	
	if (!empty($response)) {
		$response_code = is_wp_error($response) ? 'ERROR' : wp_remote_retrieve_response_code($response);
		$response_body = is_wp_error($response) ? $response->get_error_message() : wp_remote_retrieve_body($response);
		$message .= " | Response Code: {$response_code} | Response: " . substr($response_body, 0, 500);
	}
	
	payop_debug($message, 'info');
}

/**
 * Log payment processing steps
 *
 * @param int $order_id The order ID
 * @param string $step The processing step
 * @param string $details Additional details
 */
function payop_debug_payment_step($order_id, $step, $details = '') {
	$message = "Order #{$order_id} - {$step}";
	
	if (!empty($details)) {
		$message .= " | {$details}";
	}
	
	payop_debug($message, 'info');
}

/**
 * Log field validation details
 *
 * @param string $payment_method_id The payment method ID
 * @param array $fields The submitted fields
 * @param array $errors Any validation errors
 */
function payop_debug_field_validation($payment_method_id, $fields = array(), $errors = array()) {
	$message = "Field validation for payment method {$payment_method_id}";
	
	if (!empty($fields)) {
		$message .= " | Fields: " . wp_json_encode(array_keys($fields));
	}
	
	if (!empty($errors)) {
		$message .= " | Errors: " . wp_json_encode($errors);
		payop_debug($message, 'warning');
	} else {
		$message .= " | Status: PASSED";
		payop_debug($message, 'info');
	}
}

/**
 * Clear PayOp debug log
 */
function payop_clear_debug_log() {
	$upload_dir = wp_upload_dir();
	$log_file = $upload_dir['basedir'] . '/payop-debug.log';
	
	if (file_exists($log_file)) {
		unlink($log_file);
	}
}

/**
 * Get PayOp debug log contents
 *
 * @param int $lines Number of lines to return from end of file
 * @return string Log contents
 */
function payop_get_debug_log($lines = 100) {
	$upload_dir = wp_upload_dir();
	$log_file = $upload_dir['basedir'] . '/payop-debug.log';
	
	if (!file_exists($log_file)) {
		return 'No debug log found.';
	}
	
	$file_lines = file($log_file);
	if ($file_lines === false) {
		return 'Unable to read debug log.';
	}
	
	$total_lines = count($file_lines);
	$start_line = max(0, $total_lines - $lines);
	
	return implode('', array_slice($file_lines, $start_line));
}
