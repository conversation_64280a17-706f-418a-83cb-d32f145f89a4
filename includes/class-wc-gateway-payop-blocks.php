<?php
/**
 * WooCommerce Payop Payment Gateway Block.
 *
 * @final
 * @extends AbstractPaymentMethodType
 * @version 1.1.0
 */

if (!defined('ABSPATH')) {
	exit;
}

use Automattic\WooCommerce\Blocks\Payments\Integrations\AbstractPaymentMethodType;

final class WC_Gateway_Payop_Blocks extends AbstractPaymentMethodType {

	/**
	 * @var WC_Gateway_Payop The Payop payment gateway instance.
	 */
	private $gateway;

	/**
	 * @var string The name of the payment gateway.
	 */
	protected $name = PAYOP_PAYMENT_GATEWAY_NAME;

	/**
	 * @var WC_Payop_Payment_Methods The payment methods handler.
	 */
	private $payment_methods_handler;

	/**
	 * Initialize the Payop payment gateway block.
	 */
	public function initialize() {
		$this->settings = get_option('woocommerce_payop_settings', []);
		$this->gateway = new WC_Gateway_Payop();
		
		// Load payment methods handler
		require_once PAYOP_PLUGIN_PATH . '/includes/class-wc-payop-payment-methods.php';
		$this->payment_methods_handler = new WC_Payop_Payment_Methods();
		
		// Enqueue frontend styles for checkout
		add_action('wp_enqueue_scripts', array($this, 'enqueue_checkout_scripts'));
	}

	/**
	 * Enqueue checkout scripts and styles.
	 */
	public function enqueue_checkout_scripts() {
		if (is_checkout()) {
			wp_enqueue_script(
				'payop-payment-methods-checkout',
				PAYOP_PLUGIN_URL . 'js/payment-methods-checkout.js',
				array('jquery'),
				'1.0.0',
				true
			);
			
			wp_enqueue_style(
				'payop-payment-methods-checkout',
				PAYOP_PLUGIN_URL . 'css/payment-methods-checkout.css',
				array(),
				'1.0.0'
			);
		}
	}

	/**
	 * Check if the Payop payment gateway is active.
	 *
	 * @return bool Whether the payment gateway is active.
	 */
	public function is_active() {
		return $this->gateway->is_available();
	}

	/**
	 * Get the script handles required for the payment method.
	 *
	 * @return array Script handles.
	 */
	public function get_payment_method_script_handles() {
		$asset_path = PAYOP_PLUGIN_PATH . '/js/payop-blocks-integration.js';
		
		wp_register_script(
			'payop-blocks-integration',
			PAYOP_PLUGIN_URL . '/js/payop-blocks-integration.js',
			[
				'wc-blocks-registry',
				'wc-settings',
				'wp-element',
				'wp-html-entities',
				'wp-i18n',
			],
			filemtime($asset_path),
			true
		);

		// Set script translations if available.
		if (function_exists('wp_set_script_translations')) {
			wp_set_script_translations('payop-blocks-integration');
		}

		// Get available payment methods
		$available_methods = array();
		$selected_methods = array();
		
		if ($this->gateway->public_key && $this->gateway->project_id) {
			$methods = $this->payment_methods_handler->get_available_payment_methods(
				$this->gateway->public_key,
				$this->gateway->project_id
			);
			
			if (!isset($methods['error']) && !empty($methods)) {
				$available_methods = $methods;
				
				// Get selected methods in correct order
				foreach ($this->gateway->selected_payment_methods as $method_id) {
					foreach ($available_methods as $method) {
						if ($method['identifier'] == $method_id) {
							$selected_methods[] = $method;
							break;
						}
					}
				}
			}
		}
		
		wp_localize_script('payop-blocks-integration', 'payopBlockData', [
			'name' => PAYOP_PAYMENT_GATEWAY_NAME,
			'title' => $this->gateway->title,
			'description' => $this->gateway->description,
			'showLogos' => $this->gateway->show_payment_logos === 'yes',
			'availableMethods' => $available_methods,
			'selectedMethods' => $selected_methods,
			'ajaxUrl' => admin_url('admin-ajax.php'),
			'nonce' => wp_create_nonce('payop_blocks_nonce'),
			'jwtToken' => $this->gateway->jwt_token,
			'projectId' => $this->gateway->project_id,
		]);

		return ['payop-blocks-integration'];
	}

	/**
	 * Get data for the payment method.
	 *
	 * @return array Payment method data.
	 */
	public function get_payment_method_data() {
		$data = [
			'title' => $this->gateway->title,
			'description' => $this->gateway->description,
			'supports' => [
				'products',
				'payment_details',
			],
		];
		
		// If we have selected payment methods, add them to the data
		if (!empty($this->gateway->selected_payment_methods)) {
			$data['paymentMethods'] = $this->gateway->selected_payment_methods;
		}
		
		return $data;
	}
}
